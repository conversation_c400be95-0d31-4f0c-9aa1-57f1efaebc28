/* eslint no-console: 0 */

const AWS = require('aws-sdk');
const glob = require('glob');
const fs = require('fs');
const path = require('path');
const Promise = require('bluebird');

const uploadFolder = './build/';
const S3BucketToUpload = process.env.S3_BUCKET_NAME;
const AWSAccessKeyId = process.env.AWS_ACCESS_KEY_ID;
const AWSSecretAccessKey = process.env.AWS_SECRET_ACCESS_KEY;

if (!S3BucketToUpload || !AWSAccessKeyId || !AWSSecretAccessKey) {
  console.log('Please set all of the ENV variables: S3_BUCKET_NAME, AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY');
  process.exit(1);
}

const cloudFront = new AWS.CloudFront({
  accessKeyId: AWSAccessKeyId,
  secretAccessKey: AWSSecretAccessKey,
  region: 'eu-central-1'
});

const s3 = new AWS.S3({
  accessKeyId: AWSAccessKeyId,
  secretAccessKey: AWSSecretAccessKey,
  apiVersion: '2006-03-01',
  signatureVersion: 'v4',
  region: 'eu-central-1'
});

/**
 * Fetch CloudFront distribution id based on the Alias CNAME
 * @returns {Bluebird<(string|null)>}
 */
function getDistributionId() {
  return cloudFront.listDistributions().promise()
    .then(list => list.DistributionList.Items)
    .then(items => items.filter(item => item.Aliases.Items.indexOf(S3BucketToUpload) > -1))
    .then(filteredItems => (filteredItems.length ? filteredItems[0].Id : null));
}

/**
 * Invalidate Cloudfront Distribution and specific file names
 *
 * We are invalidating only the main entry point (app.js). Chunks are
 * being cached based on their filenames(hashes).
 *
 * @returns {Bluebird}
 */
function invalidateCache() {
  return getDistributionId()
    .then(id => {
      if (!id) {
        console.log('No Cloudfront distribution found. Skipping cache invalidation.');
        return true;
      }

      return cloudFront.createInvalidation({
        DistributionId: id,
        InvalidationBatch: {
          CallerReference: Date.now().toString(),
          Paths: {
            Quantity: 4,
            Items: [
              '/js/app.js',
              '/js/app.css',
              '/js/app.js.map',
              '/js/app.css.map'
            ]
          }
        }
      })
        .promise();
    });
}

/**
 * @param {string} folderToUpload The local folder whos files will be uploaded
 * @param {string} bucketName The remote bucket name
 * @returns {Bluebird}
 */
function uploadFiles(folderToUpload, bucketName) {
  const files = glob.sync(`${folderToUpload}/**/*.*`);

  const mimeTypes = {
    '.html': 'text/html',
    '.css': 'text/css',
    '.js': 'application/javascript'
  };

  const params = {
    Bucket: bucketName,
    ACL: 'public-read'
  };

  return Promise.each(files, (file) => {
    let fileObject = Object.assign({}, params, {
      Body: fs.readFileSync(file),
      Key: file.replace(uploadFolder, '')
    });

    const ext = path.parse(file).ext;
    if (mimeTypes[ext]) {
      fileObject = Object.assign({}, fileObject, {
        ContentType: mimeTypes[ext]
      });
    }

    return s3.putObject(fileObject)
      .promise()
      .then(() => {
        console.log(`${file} file uploaded`);
        return true;
      })
      .catch((err) => {
        console.log(`Error while uploading ${file}`);
        throw new Error(err);
      });
  });
}

/**
 * Create an S3 bucket web based
 * @param {string} bucketName
 * @returns {Bluebird}
 */
function createBucket(bucketName) {
  return s3.createBucket({
    Bucket: bucketName
  })
    .promise()
    .catch((err) => {
      if (err.code !== 'BucketAlreadyOwnedByYou') {
        throw new Error(err);
      }

      return true;
    })
    .then(() => s3.putBucketWebsite({
      Bucket: S3BucketToUpload,
      WebsiteConfiguration: {
        IndexDocument: {
          Suffix: 'index.html'
        },
        ErrorDocument: {
          Key: 'error.html'
        }
      }
    }).promise());
}

function startDeploy() {
  return createBucket(S3BucketToUpload)
    .then(() => uploadFiles(uploadFolder, S3BucketToUpload))
    .then(() => invalidateCache())
    .then(() => console.log('Upload done.'))
    .catch((err) => {
      console.log(err);
      process.exit(1);
    });
}

startDeploy();
