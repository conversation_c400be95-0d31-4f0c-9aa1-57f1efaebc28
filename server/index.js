const browserSync = require('browser-sync');
const path = require('path');
const webpack = require('webpack');
const webpackDevMiddleware = require('webpack-dev-middleware');
const webpackHotMiddleware = require('webpack-hot-middleware');
const webpackConfig = require('../config/webpack.config');

const bundler = webpack(webpackConfig);

/**
 * Initialize BrowserSync and add the webpack middleware
 * for serving the bundled files in hot mode.
 */
browserSync.init({
  server: {
    baseDir: path.join(__dirname, '../build'),

    middleware: [
      webpackDevMiddleware(bundler, {
        publicPath: webpackConfig.output.publicPath,

        stats: {
          colors: true
        },

        noInfo: true
      }),

      webpackHotMiddleware(bundler),
    ],
  },
});
