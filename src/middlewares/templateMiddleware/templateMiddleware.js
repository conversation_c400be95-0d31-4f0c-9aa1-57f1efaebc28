import { isQueryResult } from 'utils/apolloUtils';
import editThemeField from 'state/actions/proposals/theme/editThemeField';
import editUpsaleThemeField from 'state/actions/upsales/theme/editUpsaleThemeField';
import { PROPOSAL_REQUEST_EDIT_FIELD } from 'state/actionTypes';

import { get, set, find } from 'lodash';
import getTemplates from 'queries/getTemplates.gql';
import { getFileNameFromPath } from 'utils/stringUtils';

function getValidProposalTemplate(
  client,
  propertyCode,
  language,
  templateType,
  template,
  isRequestProposal,
  store
) {
  client
    .query({
      query: getTemplates,
      variables: {
        propertyCode,
        language,
        type: templateType,
      },
    })
    .then(({ data }) => {
      const isTemplateValid = find(
        data.templates,
        temp => getFileNameFromPath(temp.id).toLowerCase() === template
      );
      // If the template is found update the proposal
      const actionToCall = isRequestProposal ? editThemeField : editUpsaleThemeField;
      if (isTemplateValid) {
        store.dispatch(actionToCall({ fieldName: 'template', fieldValue: template }, 'system'));
      } else if (!isTemplateValid) {
        store.dispatch(actionToCall({ fieldName: 'template', fieldValue: '' }, 'system'));
      }
    });
}

/**
 * Middleware that checks if the current template field exists in the templates
 */
const createTemplateMiddleware = client => store => next => action => {
  const isUpsaleProposal = isQueryResult(action, 'getUpsaleProposal');
  let isRequestProposal = isQueryResult(action, 'getRequest');

  // Check when we get a proposal result
  if (isUpsaleProposal || isRequestProposal) {
    const isDraft = get(action, 'result.data.proposal.state') === 'draft';
    const template = get(action, 'result.data.proposal.template');

    // If there is a selected template and the proposal state is draft
    if (template && isDraft) {
      // Set the template to empty until we verify it.
      set(action, 'result.data.proposal.template', '');

      // Get all the templates and check if the one we have matches them
      const propertyCode = get(action, 'result.data.proposal.request.propertyCode');
      const language = get(action, 'result.data.proposal.language');
      const templateType = isRequestProposal ? 'templates' : 'upsaleTemplates';

      getValidProposalTemplate(
        client,
        propertyCode,
        language,
        templateType,
        template,
        isRequestProposal,
        store
      );
    }
  }

  // When the user changes the property code
  if (
    get(action, 'type') === PROPOSAL_REQUEST_EDIT_FIELD &&
    get(action, 'payload.fieldName') === 'propertyCode'
  ) {
    const state = store.getState();
    const proposal = get(state, 'proposals.proposal');

    const propertyCode = get(action, 'payload.fieldValue');
    const language = get(proposal, 'theme.language', 'en');
    const template = get(proposal, 'theme.template');
    const templateType = 'templates';
    isRequestProposal = true;

    getValidProposalTemplate(
      client,
      propertyCode,
      language,
      templateType,
      template,
      isRequestProposal,
      store
    );
  }

  return next(action);
};

export default createTemplateMiddleware;
