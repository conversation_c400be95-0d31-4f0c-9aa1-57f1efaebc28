/* global window */
import RequestPersistor from 'helpers/persistor';
import { createRequestVariablesSelector } from 'state/selectors/mutationVariablesSelectors';
import { getUserOperatorId, getUserAccountName } from 'state/selectors/userSelectors';
import { throttle } from 'lodash';

const shouldSaveRequest = state => state.steps.userStepProgress > 0;
const isRequestNew = state => !state.request.requestId;

const saveRequest = store => {
  const state = store.getState();

  if (isRequestNew(state) && shouldSaveRequest(state)) {
    const request = createRequestVariablesSelector(state);
    const userId = getUserOperatorId(state);
    const accountName = getUserAccountName(state);

    RequestPersistor.saveRequest(`${accountName}.${userId}`, {
      ...request,
      request: {
        ...request.request,
        property: {
          name: state.request.propertyName,
        },
      },
    });
  }
};

const throttledSaveRequest = throttle(saveRequest, 800);

const persistanceMiddleware = store => next => action => {
  if (window && window.location && window.location.hash === '#/request/new') {
    throttledSaveRequest(store);
  }

  return next(action);
};

export default persistanceMiddleware;
