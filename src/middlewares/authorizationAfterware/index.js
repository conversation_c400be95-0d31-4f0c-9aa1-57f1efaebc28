import moment from 'moment';
import { getJWT } from 'utils/jwtUtils';
import editUserAuthorized from 'state/actions/user/editUserAuthorized';

const isExpired = expirationDate =>
  expirationDate && moment(Date.now()).isAfter(moment.unix(expirationDate));

const authorizationAfterWare = store => ({
  applyAfterware({ response }, next) {
    const { parsedToken } = getJWT();
    if ((parsedToken.exp && isExpired(parsedToken.exp)) || response.status === 401) {
      store.dispatch(editUserAuthorized(false));
    }
    return next(response);
  },
});

export default authorizationAfterWare;
