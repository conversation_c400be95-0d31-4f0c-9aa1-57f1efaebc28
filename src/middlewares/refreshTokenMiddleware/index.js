/* global localStorage */
import { throttle, get } from 'lodash';
import moment from 'moment';
import refreshToken from 'queries/refreshToken.gql';
import { parseJwt } from 'utils/jwtUtils';
import { getUserSessionExpiration } from 'state/selectors/user';
import editUserSessionExpiration from 'state/actions/user/editUserSessionExpiration';

const isTokenExpiringSoon = tokenExpirationTimestamp => {
  // Get the token expiration unix date as a moment obj.
  const expirationDate = moment.unix(tokenExpirationTimestamp);
  // Get the difference of the token timestamp with the current date in minutes without presicion
  const differece = expirationDate.diff(moment(), 'minutes', false);
  // Return true if the difference is less than 5 minutes
  return differece < 20;
};

/**
 * Middleware that checks if the token is expiring soon and automatically updates it
 */
const createRefreshTokenMiddleware = client => {
  // Check for a valid token every minute, if in throttle time it will return undefined
  const throttledForTokenExpiration = throttle(isTokenExpiringSoon, 60000);
  let isFetchingToken = false;
  return store => next => action => {
    const tokenExpiresAt = getUserSessionExpiration(store.getState());
    if (tokenExpiresAt && throttledForTokenExpiration(tokenExpiresAt) && !isFetchingToken) {
      // Indicate that we are fetching the token
      isFetchingToken = true;

      client.query({ query: refreshToken }).then(({ data }) => {
        isFetchingToken = false;
        // Clear the throtled function so that it will rerun
        throttledForTokenExpiration.cancel();

        const token = get(data, 'refreshToken.token');

        // Update the token at the localStorage
        localStorage.setItem('token', token);

        const tokenExpiration = parseJwt(token).exp;
        // Update the expiresAt in the state
        store.dispatch(editUserSessionExpiration(tokenExpiration));
      });
    }

    return next(action);
  };
};

export default createRefreshTokenMiddleware;
