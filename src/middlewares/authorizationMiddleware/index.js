/* global localStorage __CONFIG__ window */
import { getJWT } from 'utils/jwtUtils';

const createAuthMiddleware = () => ({
  applyMiddleware(req, next) {
    const { token } = getJWT();
    // Create the header object if needed.
    if (!req.options.headers) {
      req.options.headers = {};
    }

    req.options.headers.authorization = `Bearer ${token}`;
    next();
  },
});

export default createAuthMiddleware;
