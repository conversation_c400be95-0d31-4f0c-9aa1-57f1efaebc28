import { some, get } from 'lodash';
import { PROPOSAL_THEME_EDIT_FIELD, UPSALES_PROPOSAL_THEME_EDIT_FIELD } from 'state/actionTypes';

import editOfferField from 'state/actions/proposals/offers/editOfferField';
import editUpsaleOfferField from 'state/actions/upsales/offers/editUpsaleOfferField';

import getOfferLanguageData from 'queries/getOfferLanguageData.gql';

/**
 * Takes the action type and returns the corresponding action
 * @param {String} actionType
 */
const getAction = (actionType = '') =>
  ({
    [PROPOSAL_THEME_EDIT_FIELD]: editOfferField,
    [UPSALES_PROPOSAL_THEME_EDIT_FIELD]: editUpsaleOfferField,
  }[actionType] || undefined);

/**
 * Takes the actionType and the state and returns
 * the mapping to the proposal offers
 * @param {String} actionType
 * @param {Object} state The global state tree
 */
const getOffersByActionType = (actionType, state) =>
  ({
    [PROPOSAL_THEME_EDIT_FIELD]: state.proposals.proposal.offers,
    [UPSALES_PROPOSAL_THEME_EDIT_FIELD]: state.upsalesProposals.proposal.offers,
  }[actionType] || undefined);

/**
 * Apollo Middleware to catch the changeLanguage action
 * and update the offers' title - description of the current proposal
 *
 * @param {ApolloClient} client
 */
const createLanguageMiddleware = client => store => next => action => {
  // Get the action to dispatch
  const actionToCall = getAction(action.type);
  const isLanugageUpdate = get(action, 'payload.fieldName') === 'language';
  const isActionByUser = get(action, 'meta.source') === 'user';

  if (actionToCall && isLanugageUpdate && isActionByUser) {
    const state = store.getState();
    const { fieldValue: language } = action.payload;

    // Get the current proposal's offers
    const offers = getOffersByActionType(action.type, state);

    const allOffers = offers.all.map(id => offers.byId[id]);

    // Map through each offer and update it's title and description with the new
    // results based on the new selected language
    // @NOTE: Multiple offers with the same rates will result in cache lookup
    allOffers.forEach(offer => {
      const { id, propertyCode, accommodation, rateId } = offer;

      const variables = {
        roomCode: accommodation && accommodation.length ? accommodation[0].code : '',
        propertyCode,
        rateId,
      };

      const areVariablesValid = !some(variables, variable => !variable);

      // If we have all required variables call the query and
      // update the state
      if (areVariablesValid) {
        client
          .query({
            query: getOfferLanguageData,
            variables: {
              roomCode: accommodation[0].code,
              propertyCode,
              language,
              rateId,
            },
          })
          .then(({ data }) => {
            const roomName = get(data, 'room.name');
            const rateName = get(data, 'rate.name');

            const currentState = store.getState();
            const currentOffers = getOffersByActionType(action.type, currentState);

            if (currentOffers.byId[id]) {
              store.dispatch(
                actionToCall({ id, fieldName: 'title', fieldValue: roomName }, 'system')
              );
              store.dispatch(
                actionToCall({ id, fieldName: 'description', fieldValue: rateName }, 'system')
              );
            }
          });
      }
    });
  }

  return next(action);
};

export default createLanguageMiddleware;
