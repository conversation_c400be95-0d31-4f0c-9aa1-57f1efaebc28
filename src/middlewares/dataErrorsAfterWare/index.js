import { get, each } from 'lodash';
import addNotification from 'state/actions/notifications/addNotification';

const dataErrorsAfterWare = (store, errorReporter = null) => ({
  applyAfterware({ response }, next) {
    // Clone the response
    const res = response.clone();

    if (!res.ok) {
      if (res.status === 500) {
        if (errorReporter) {
          errorReporter.captureException(new Error('500 Error'), {
            level: 'info',
          });
        }
        store.dispatch(
          addNotification({
            type: 'error',
            header: 'Error',
            content: 'An unexpected error occurred',
          })
        );
      }
      return next();
    }

    // Handle Apollo Errors
    return res.json().then(json => {
      const hasErrors = get(json, 'errors.length') > 0;
      if (hasErrors) {
        const errorMessage = get(json, 'errors[0].message') || 'An unexpected error occured';

        if (errorReporter) {
          errorReporter.captureException(new Error(errorMessage), {
            level: 'warning',
          });
        }

        store.dispatch(
          addNotification({
            type: 'error',
            header: 'Error',
            content: get(json, 'errors[0].message') || 'An unexpected error occured',
          })
        );
      } else {
        each(json.data, data => {
          if (data && data.errors && data.errors.length) {
            const errorMessage = get(data, 'errors[0].message') || 'An unexpected error occured';

            if (errorReporter) {
              errorReporter.captureException(new Error(errorMessage), {
                level: 'warning',
              });
            }

            store.dispatch(
              addNotification({
                type: 'error',
                header: 'Error',
                content: errorMessage,
              })
            );
          }
        });
      }

      return next();
    });
  },
});

export default dataErrorsAfterWare;
