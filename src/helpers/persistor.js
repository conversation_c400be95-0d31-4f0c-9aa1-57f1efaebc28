import localforage from 'localforage';

/**
 * An instance of the localforage created specifically
 * to save/retrieve/clear a request object to the user's browser.
 */
class RequestPersistor {
  constructor() {
    this.persistor = localforage.createInstance({
      name: 'quotelier',
      version: 1.0,
    });

    this.persistor.config();
  }

  /**
   * Takes a request and returns a promise that resolves
   * when the request object has been saved to the local[forage].
   *
   * @param {Object} request The request object
   * @return {Promise} A promise that returns the saved request.
   */
  saveRequest = (userId, request) => this.persistor.setItem(`${userId}.request`, request);

  /**
   * Returns a promise that resolves with the saved reuqest if we have one saved in the storage
   *
   * @return {Promise} A promise that returns the saved request.
   */
  getRequest = userId => this.persistor.getItem(`${userId}.request`);

  clear = () => this.persistor.clear();
}

const RequestPersistorSingleton = new RequestPersistor();

export default RequestPersistorSingleton;
