const state = {
  request: {
    propertyCode: 'demo',
    adults: 2,
    children: 0,
    infants: 0,
    checkIn: '2017-08-22',
    nights: 2,
    rooms: 1,
    location: '',
    channel: '',
    message: '',
    releaseAt: '',
    requestId: '',
    isFormSubmited: false,
  },
  availableRates: {
    byId: {
      28134: {
        id: 28134,
        remaining: 1,
        room: 'Junior Suite',
        accommodationCode: 'JSUI',
        rate: 'Rates for Non Locals ',
        pricing: {
          price: '250.00',
          discount: '0.00',
          stay: '250.00',
          extras: 0,
          taxes: 0,
        },
        releaseAt: '2017-06-22',
        isSelected: true,
        rateId: 28134,
        currency: 'EUR',
        propertyCode: 'demo',
        checkIn: '2017-08-22',
        nights: 2,
        adults: 2,
        children: 0,
        rooms: 1,
        infants: 0,
      },
    },
    all: [28134],
    loading: false,
    noDataFound: false,
  },
  offers: {
    byId: {
      0: {
        propertyCode: 'demo',
        title: 'Junior Suite',
        description: 'Rates for Non Locals ',
        checkIn: '2017-08-22',
        nights: 2,
        releaseAt: '2017-06-22',
        rooms: 1,
        currency: 'EUR',
        adults: 2,
        children: 0,
        infants: 0,
        rateId: 28134,
        policies: {
          cancellation:
            '<p>If cancelled up to 1 day before date of  arrival,no fee will be charged.If  cancelled or modified later or in case of no show, the first night will be charged.</p>',
          payment:
            '<p>\n\tCredit card guarantee is required. Your credit card will be charge accordingly in case of cancellation</p>\n',
        },
        officialRate: '250.00',
        roomRate: '250.00',
        discountRate: '0.00',
        taxes: 0,
        accommodation: ['JSUI'],
        services: [],
        serviceCodes: [],
        id: 0,
      },
    },
    all: [0],
    active: 0,
  },
  contact: {
    name: 'James Gatzos',
    title: 'mr',
    email: '<EMAIL>',
    nickname: 'Mr James Gatzos',
    country: 'GR',
    phone: '12345678890',
    channel: 'email',
    category: ['Developer', 'Couple'],
    categories: [],
    notes: 'Some awesome internal notes',
    isNicknameDirty: false,
  },
  theme: {
    template: 'proposal',
    language: 'en',
    message:
      '<p>\n  Dear Mr James Gatzos,\n</p>\n<p>\n  Thank you for your interest in Demo Hotel for your guaranteed relaxation.\n</p>\n<p>\n  Below you will find our proposal and useful information about your stay.\n</p>\n<p>\n  In case that you have queries, feel free to contact your holiday consultant directly.\n</p>\n<p>\n  Kind regards,\n</p>\n<p>\n  George\n</p>',
    isMessageDirty: true,
  },
  steps: {
    activeStep: 4,
  },
  user: {
    accountName: 'DEMO',
    language: 'en',
    id: '',
    state: '',
    muted: '',
    operatorId: '<EMAIL>',
    mailout: {},
    audit: {},
  },
  filters: {
    state: 'all',
    onlyOwned: false,
    propertyCode: 'all',
    term: '',
  },
  notifications: {
    byId: {
      *************: {
        id: *************,
        isEnabled: false,
        type: 'success',
        header: 'Request Send',
        content: 'The request has been sent successfully',
      },
      *************: {
        id: *************,
        isEnabled: true,
        type: 'success',
        header: 'Request Send',
        content: 'The request has been sent successfully',
      },
    },
    all: [*************],
  },
};

export { state };

export default state;
