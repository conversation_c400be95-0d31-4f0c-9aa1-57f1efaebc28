/* global __CONFIG__ __webpack_hash__ window */
/* eslint-disable camelcase */
/* eslint-disable no-underscore-dangle */
/* eslint-disable consistent-return */
import { createStore, combineReducers, applyMiddleware, compose } from 'redux';
import ApolloClient, { createNetworkInterface } from 'apollo-client';
import <PERSON> from 'raven-js';

// Reducers
import notifications from 'state/reducers/notifications';
import proposals from 'state/reducers/proposals';
import cms from 'state/reducers/cms';
import upsalesProposals from 'state/reducers/upsalesProposals';
import search from 'state/reducers/search';
import rates from 'state/reducers/rates';
import opportunities from 'state/reducers/opportunities';
import waitingList from 'state/reducers/waitingList';
import user from 'state/reducers/user';
import events from 'state/reducers/events';
import upgrades from 'state/reducers/upgrades';

// Middlewares / Afterwares
import createRavenMiddleware from 'raven-for-redux';
import createLanguageMiddleware from 'middlewares/languageMiddleware';
import createTemplateMiddleware from 'middlewares/templateMiddleware';
import createAuthMiddleware from 'middlewares/authorizationMiddleware';
import dataErrorsAfterWare from 'middlewares/dataErrorsAfterWare';
import authorizationAfterware from 'middlewares/authorizationAfterware';
import createRefreshTokenMiddleware from 'middlewares/refreshTokenMiddleware';

const createClientAndStore = () => {
  let errorReporter = null;

  if (__CONFIG__.sentry.enabled) {
    Raven.config(__CONFIG__.sentry.dsn, {
      environment: __CONFIG__.env,
      release: __CONFIG__.PRODUCTION ? __CONFIG__.version : __webpack_hash__,
    }).install();

    errorReporter = Raven;
  }

  // Create the network interface
  const networkInterface = createNetworkInterface({
    uri: __CONFIG__.graphQL.endpoint,
  });

  // Create the Apollo Client Instance
  const client = new ApolloClient({
    networkInterface,
    dataIdFromObject: o => {
      if (o.__typename === 'EmailTemplate') {
        return;
      }

      return o.id;
    },
    addTypename: false,
  });

  const authMiddleware = createAuthMiddleware();
  networkInterface.use([authMiddleware]);

  const languageMiddleware = createLanguageMiddleware(client);
  const templateMiddleware = createTemplateMiddleware(client);
  const refreshTokenMiddleware = createRefreshTokenMiddleware(client);

  const reduxMiddlewares = [
    client.middleware(),
    languageMiddleware,
    templateMiddleware,
    refreshTokenMiddleware,
  ];

  if (__CONFIG__.sentry.enabled) {
    reduxMiddlewares.push(
      createRavenMiddleware(Raven, {
        breadcrumbDataFromAction: action => {
          const isApolloAction = /^APOLLO/.test(action.type);

          if (!isApolloAction) {
            return {
              action: JSON.stringify(action.payload),
            };
          }
        },
      })
    );
  }

  // Create the Redux Store
  const store = createStore(
    combineReducers({
      user,
      rates,
      events,
      proposals,
      search,
      upsalesProposals,
      notifications,
      opportunities,
      waitingList,
      upgrades,
      cms,
      apollo: client.reducer(),
    }),
    {},
    compose(
      applyMiddleware(...reduxMiddlewares),
      window.devToolsExtension ? window.devToolsExtension() : f => f
    )
  );

  networkInterface.useAfter([
    dataErrorsAfterWare(store, errorReporter),
    authorizationAfterware(store),
  ]);

  return { store, client };
};

export default createClientAndStore;
