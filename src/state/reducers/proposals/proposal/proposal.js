import { get } from 'lodash';
import { isQueryResult } from 'utils/apolloUtils';

import {
  PROPOSAL_INITIALIZE,
  PROPOSAL_CLEAR_CACHE,
  PROPOSAL_TOGGLE_MUTED,
  PROPOSAL_EDIT_ACTIVE_SECTION,
  PROPOSAL_TOGGLE_LOADING,
  PROPOSAL_TOGGLE_MARKED_TO_FOLLOW_UP,
  PROPOSAL_CLOSE_NOTES_FORM,
  PROPOSAL_CHANGE_OPERATOR,
  PROPOSAL_OPTIONS_EDIT_FIELD,
  PROPOSAL_ATTRIBUTES_EDIT_FIELD
} from 'state/actionTypes';

import request, { initialState as requestState } from './request';
import contact, { initialState as contactState } from './contact';
import offers, { initialState as offersState } from './offers';
import theme, { initialState as themeState } from './theme';
import options, { initialState as optionsState } from './options';
import dirty from './dirty';

// Parse attributes from server - handle both JSON strings and objects
const getProposalAttributes = ({ attributes }) => {
  // If attributes is null or undefined, return empty object
  if (!attributes) {
    return {};
  }

  // If attributes is already an object, return it as-is
  if (typeof attributes === 'object') {
    return attributes;
  }

  // If attributes is a string, try to parse it as JSON
  if (typeof attributes === 'string') {
    // Handle empty object string case
    if (attributes === '{}') {
      return {};
    }

    try {
      const parsed = JSON.parse(attributes);
      return parsed || {};
    } catch (error) {
      console.warn('Failed to parse proposal attributes JSON:', attributes, error);
      return {};
    }
  }

  // Fallback to empty object for any other type
  return {};
};

const initialState = {
  id: '',
  state: '',
  template: '',
  emailTemplate: '',
  proposalUri: '',
  muted: false,
  markedToFollowUp: '',
  language: 'en',
  notes: '',
  createdAt: '',
  operator: {
    id: '',
    fullName: '',
    photo: ''
  },
  request: requestState,
  contact: contactState,
  offers: offersState,
  theme: themeState,
  activeSection: 'contact',
  isLoading: false,
  isDirty: false,
  options: optionsState,
  hasOfferOptions: true,
  attributes: {}
};

const hasOfferOptions = proposal => get(proposal, 'offers[0].optionIds.length', 0) > 0;

const proposalReducer = (state = initialState, action) => {
  if (isQueryResult(action, 'getRequest')) {
    const { proposal } = action.result.data;
    return {
      ...initialState,
      ...proposal,
      hasOfferOptions: hasOfferOptions(proposal),
      isLoading: state.isLoading,
      notes: proposal.request.notes,
      operator: {
        ...proposal.operator,
        photo:
          proposal.operator && proposal.operator.photo
            ? proposal.operator.photo.small || proposal.operator.photo.medium
            : ''
      },
      request: request(state.request, action),
      offers: offers(state.offers, action),
      contact: contact(state.contact, action),
      theme: theme(state.theme, action),
      options: proposal.options,
      attributes: getProposalAttributes(proposal)
    };
  }

  switch (action.type) {
    case PROPOSAL_INITIALIZE: {
      const { proposal, normalizedProposal } = action.payload;

      if (normalizedProposal) {
        return {
          ...initialState,
          ...normalizedProposal
        };
      }

      return {
        ...initialState,
        ...proposal,
        notes: proposal.request.notes,
        operator: {
          ...proposal.operator,
          id: proposal.operator ? proposal.operator.id || proposal.operatorId : proposal.operatorId,
          photo:
            proposal.operator && proposal.operator.photo
              ? proposal.operator.photo.small || proposal.operator.photo.medium
              : ''
        },
        request: request(state.request, action),
        offers: offers(state.offers, action),
        contact: contact(state.contact, action),
        theme: theme(state.theme, action),
        options: proposal.options,
        attributes: getProposalAttributes(proposal)
      };
    }

    case PROPOSAL_CLEAR_CACHE: {
      return initialState;
    }

    case PROPOSAL_TOGGLE_MUTED: {
      return {
        ...state,
        muted: !state.muted
      };
    }

    case PROPOSAL_EDIT_ACTIVE_SECTION: {
      const { activeSection } = action.payload;
      return {
        ...state,
        activeSection
      };
    }

    case PROPOSAL_TOGGLE_LOADING: {
      const { loading } = action.payload;

      return {
        ...state,
        isLoading: loading
      };
    }

    case PROPOSAL_TOGGLE_MARKED_TO_FOLLOW_UP: {
      return {
        ...state,
        markedToFollowUp: !state.markedToFollowUp
      };
    }

    case PROPOSAL_CLOSE_NOTES_FORM: {
      return {
        ...state,
        activeSection: '',
        isDirty: false
      };
    }

    case PROPOSAL_CHANGE_OPERATOR: {
      const { operator } = action.payload;

      return {
        ...state,
        operator
      };
    }

    case PROPOSAL_OPTIONS_EDIT_FIELD: {
      return {
        ...state,
        options: options(state.options, action)
      };
    }

    case PROPOSAL_ATTRIBUTES_EDIT_FIELD: {
      const { fieldName, fieldValue } = action.payload;

      return {
        ...state,
        attributes: {
          ...state.attributes,
          [fieldName]: fieldValue
        }
      };
    }

    default:
      return {
        ...state,
        isDirty: dirty(state.isDirty, action),
        request: request(state.request, action),
        offers: offers(state.offers, action),
        contact: contact(state.contact, action),
        theme: theme(state.theme, action)
      };
  }
};

export { initialState };
export default proposalReducer;
