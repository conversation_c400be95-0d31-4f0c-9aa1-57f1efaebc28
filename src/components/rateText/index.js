import { graphql, compose } from 'react-apollo';
import getRate from 'queries/getRate.gql';
import RateText from './rateText';

const RateTextContainer = graphql(getRate, {
  props: ({ data }) => ({
    text: data.rate ? data.rate.name : '',
  }),
  skip: ownProps =>
    !ownProps.propertyCode || !ownProps.language || !ownProps.rateId || !ownProps.roomCode,
});

export default compose(RateTextContainer)(RateText);
