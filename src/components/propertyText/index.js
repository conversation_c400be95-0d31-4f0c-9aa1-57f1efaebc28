import { graphql } from 'react-apollo';
import { connect } from 'react-redux';
import { find } from 'lodash';

import getProperties from 'queries/getAccountProperties.gql';

import { getProperties as getQueryVariables } from 'state/selectors/queryVariables';

import PropertyText from './propertyText';

const PropertyTextDataContainer = graphql(getProperties, {
  props: ({ ownProps, data }) => {
    const selectedProperty = data.properties
      ? find(data.properties, { code: ownProps.propertyCode })
      : {};

    return {
      propertyName: selectedProperty.name,
    };
  },
  skip: ownProps => !ownProps.language || !ownProps.propertyCode,
})(PropertyText);

const PropertyTextContainer = connect(state => ({
  ...getQueryVariables(state),
}))(PropertyTextDataContainer);

export default PropertyTextContainer;
