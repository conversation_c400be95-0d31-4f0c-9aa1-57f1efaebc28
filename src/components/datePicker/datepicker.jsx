import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { SingleDatePicker } from 'react-dates';
import { isMobile, isTablet } from 'utils/mediaQueries';
import { Label } from 'semantic-ui-react';

const DISPLAY_FORMAT = 'DD-MM-YYYY';
const STATE_FORMAT = 'YYYY-MM-DD';

/**
 * A datepicker that selects a single date.
 *
 * @class SingleDatePickerWrapper
 * @extends {Component}
 */
class SingleDatePickerWrapper extends Component {
  static propTypes = {
    onDateChange: PropTypes.func,
    value: PropTypes.string,
    placeholder: PropTypes.string,
    id: PropTypes.string.isRequired,
    withPortal: PropTypes.bool,
    showClearDate: PropTypes.bool,
    isOutsideRange: PropTypes.func,
    hasError: PropTypes.bool,
    errorMessage: PropTypes.string,
  };

  static defaultProps = {
    withPortal: true,
    isOutsideRange: () => {},
    hasError: false,
  };

  constructor(props) {
    super(props);

    this.state = {
      focused: false,
    };
  }

  /**
   * Called when a date is selected from the datePicker
   */
  onDateChange = date => {
    // Format the moment date before calling the props handler.
    const formatedDate = date ? moment(date).format(STATE_FORMAT) : null;
    this.props.onDateChange(formatedDate);
  };

  /**
   * Called when the focus on the input changes
   */
  onFocusChange = ({ focused }) => {
    if (focused !== this.state.focused) {
      this.setState({ focused });
    }
  };

  render() {
    const { focused } = this.state;
    const {
      value,
      id,
      placeholder,
      withPortal,
      showClearDate,
      isOutsideRange,
      hasError,
      errorMessage,
    } = this.props;

    // Transform the value into a moment obj.
    const date = value ? moment(value, STATE_FORMAT) : null;

    return (
      <div className="qb-datepicker">
        <SingleDatePicker
          initialVisibleMonth={() => date || moment()}
          displayFormat={DISPLAY_FORMAT}
          withPortal={withPortal}
          placeholder={placeholder}
          withFullScreenPortal={isMobile() || isTablet()}
          id={id}
          showClearDate={showClearDate}
          date={date}
          focused={focused}
          isOutsideRange={isOutsideRange}
          numberOfMonths={isMobile() || isTablet() ? 1 : 2}
          onDateChange={this.onDateChange}
          onFocusChange={this.onFocusChange}
          hideKeyboardShortcutsPanel
        />
        {hasError && (
          <Label
            pointing
            basic
            color="yellow"
            content={errorMessage || 'Please select a valid date'}
          />
        )}
      </div>
    );
  }
}

export default SingleDatePickerWrapper;
