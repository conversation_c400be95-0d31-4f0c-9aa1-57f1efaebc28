$react-dates-width-input: 130px !default;
$react-dates-width-arrow: 24px !default;
$react-dates-width-tooltip-arrow: 20px !default;
$react-dates-width-day-picker: 300px !default;
$react-dates-spacing-vertical-picker: 72px !default;

$react-dates-color-primary: #00a699 !default;
$react-dates-color-primary-dark: #00514a !default;
$react-dates-color-primary-shade-1: #33dacd !default;
$react-dates-color-primary-shade-2: #66e2da !default;
$react-dates-color-primary-shade-3: #80e8e0 !default;
$react-dates-color-primary-shade-4: #b2f1ec !default;
$react-dates-color-secondary: #007a87 !default;
$react-dates-color-white: #fff !default;
$react-dates-color-gray: #565a5c !default;
$react-dates-color-gray-dark: darken($react-dates-color-gray, 10.5%) !default;
$react-dates-color-gray-light: lighten($react-dates-color-gray, 17.8%) !default; // #82888a
$react-dates-color-gray-lighter: lighten($react-dates-color-gray, 45%) !default; // #cacccd

$react-dates-color-border: #dbdbdb !default;
$react-dates-color-border-light: #dce0e0 !default;
$react-dates-color-border-medium: #c4c4c4 !default;
$react-dates-color-placeholder-text: #757575 !default;
$react-dates-color-text: #484848 !default;
$react-dates-color-text-focus: #007a87 !default;
$react-dates-color-focus: #99ede6 !default;

.CalendarDay.CalendarDay--blocked.CalendarDay--blocked-calendar {
  color: #cacccd;
  background: #fff;
  border: 1px solid #e4e7e7;
  cursor: default;

  &.CalendarDay--selected-start.CalendarDay--selected-span,
  &.CalendarDay--selected-end {
    background: #c5f3ef !important;
  }

  &.CalendarDay--selected-span {
    background: #eaf7f6 !important;
  }
}

.DateRangePickerInput,
.DateInput {
  cursor: pointer;
  line-height: 1em;
  white-space: normal;
  outline: 0;
  background: #fff;
  display: inline-block;
  padding: 0 !important;
  color: rgba(0, 0, 0, 0.87);
  box-shadow: none;
  font-family: Lato, "Helvetica Neue", Arial, Helvetica, sans-serif;
  font-size: 14px;
}

.DateRangePickerInput,
.SingleDatePickerInput {
  display: flex;
  text-align: center;
  align-items: center;
  justify-content: center;
  padding: 2px !important;
  font-size: 14px;
  border: 1px solid rgba(34, 36, 38, 0.15);
  border-radius: 0.28571429rem;
}

.error {
  .DateInput__display-text {
    background: #fff6f6;
  }

  .DateRangePickerInput {
    background-color: #fff6f6;
    border: 1px solid #e0b4b4;
  }
}

.SingleDatePicker__picker.SingleDatePicker__picker--portal,
.DateRangePicker__picker.DateRangePicker__picker--portal {
  z-index: 10;
}

.DateInput__display-text,
.DateInput__display-text--has-input {
  text-align: left;
}

button.SingleDatePickerInput__clear-date,
button.DateRangePickerInput__clear-dates {
  padding: 7px 9.5px 10px;
  color: white;
  border-radius: 50%;

  &:hover {
    background: #fedfe5;

    .DateRangePickerInput__close-icon svg,
    .DateRangePickerInput__close svg {
      fill: #fc627f;
    }
  }
}

.qb-date-range-picker {
  display: flex;
  flex-direction: column;

  .ui.label {
    width: 290px;
    text-align: center;
  }

  &.is-fluid {
    .DateRangePickerInput {
      width: 100%;
      display: flex;

      .DateInput {
        font-size: 16px;

        .DateInput__display-text,
        .DateInput__display-text--has-input {
          text-align: center;
        }
      }

      .DateRangePickerInput__arrow svg {
        width: 20px;
        height: 20px;
      }
    }
  }
}

// @HACK We use this zIndex property to avoid the calendar being shown before the fullscreen modal

.ui.page.modals + div > .DateRangePicker__picker--portal,
.ui.page.modals + div > .SingleDatePicker__picker--portal {
  z-index: 1000;
}

@import "~react-dates/css/styles.scss";
