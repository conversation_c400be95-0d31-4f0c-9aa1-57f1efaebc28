/* eslint-disable react/no-unused-prop-types */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { omit } from 'lodash';

import Measure from 'react-measure';

import { START_DATE, END_DATE, HORIZONTAL_ORIENTATION, ISO_FORMAT } from 'react-dates/constants';

import { DayPickerRangeController, isInclusivelyAfterDay } from 'react-dates';

import { isMobile } from 'utils/mediaQueries';

const propTypes = {
  // example props for the demo
  autoFocusEndDate: PropTypes.bool,
  initialStartDate: PropTypes.object,
  initialEndDate: PropTypes.object,

  keepOpenOnDateSelect: PropTypes.bool,
  minimumNights: PropTypes.number,
  isOutsideRange: PropTypes.func,
  isDayBlocked: PropTypes.func,
  isDayHighlighted: PropTypes.func,

  // DayPicker props
  enableOutsideDays: PropTypes.bool,
  numberOfMonths: PropTypes.number,
  orientation: PropTypes.string,
  withPortal: PropTypes.bool,
  initialVisibleMonth: PropTypes.func,
  renderCalendarInfo: PropTypes.func,

  navPrev: PropTypes.node,
  navNext: PropTypes.node,

  onPrevMonthClick: PropTypes.func,
  onNextMonthClick: PropTypes.func,
  onOutsideClick: PropTypes.func,
  renderDay: PropTypes.func,

  // i18n
  monthFormat: PropTypes.string,

  onDatesChange: PropTypes.func,
  checkIn: PropTypes.string,
  nights: PropTypes.number,
};

const defaultProps = {
  // example props for the demo
  autoFocusEndDate: true,
  initialStartDate: null,
  initialEndDate: null,

  // day presentation and interaction related props
  renderDay: null,
  minimumNights: 1,
  isDayBlocked: day => !isInclusivelyAfterDay(day, moment()),
  isDayHighlighted: () => false,
  enableOutsideDays: false,

  // calendar presentation and interaction related props
  orientation: HORIZONTAL_ORIENTATION,
  withPortal: false,
  numberOfMonths: 3,
  onOutsideClick() {},
  keepOpenOnDateSelect: false,
  renderCalendarInfo: null,

  // navigation related props
  navPrev: null,
  navNext: null,
  onPrevMonthClick() {},
  onNextMonthClick() {},

  // internationalization
  monthFormat: 'MMMM YYYY',
};

class DayPickerRangeControllerWrapper extends Component {
  constructor(props) {
    super(props);

    this.state = {
      focusedInput: props.checkIn ? END_DATE : START_DATE,
      instanceKey: 0,
      dimensions: {
        width: -1,
        height: -1,
      },
    };
  }

  componentWillReceiveProps({ checkIn, nights }) {
    if (!this.props.checkIn && (checkIn && nights)) {
      this.setState({ instanceKey: this.state.instanceKey + 1 });
    }
  }

  onDatesChange = ({ startDate, endDate }) => {
    const clonedEnd = endDate ? endDate.clone().format('L') : null;
    const clonedStart = startDate ? startDate.clone().format('L') : null;

    const checkIn = startDate ? moment(startDate).format(ISO_FORMAT) : null;
    const nights = startDate && endDate ? moment(clonedEnd).diff(moment(clonedStart), 'days') : 0;

    this.setState({ startDate, endDate }, () => {
      this.props.onDatesChange({ checkIn, nights });
    });
  };

  onFocusChange = focusedInput => {
    this.setState({
      // Force the focusedInput to always be truthy so that dates are always selectable
      focusedInput: !focusedInput ? START_DATE : focusedInput,
    });
  };

  render() {
    const { focusedInput, dimensions: { width } } = this.state;

    const { checkIn, nights } = this.props;

    const props = omit(this.props, [
      'autoFocus',
      'autoFocusEndDate',
      'initialStartDate',
      'initialEndDate',
      'checkIn',
      'nights',
    ]);

    const startDate = checkIn ? moment(checkIn, ISO_FORMAT) : null;
    const endDate = checkIn && nights ? moment(checkIn, ISO_FORMAT).add(nights, 'days') : null;

    return (
      <Measure
        bounds
        onResize={contentRect => {
          this.setState({ dimensions: contentRect.bounds });
        }}
      >
        {({ measureRef }) => (
          <div ref={measureRef} style={{ width: '100%' }}>
            <DayPickerRangeController
              {...props}
              initialVisibleMonth={() => (checkIn ? moment(checkIn, ISO_FORMAT) : moment())}
              key={this.state.instanceKey}
              numberOfMonths={isMobile() || width < 618 ? 1 : this.props.numberOfMonths}
              onDatesChange={this.onDatesChange}
              onFocusChange={this.onFocusChange}
              focusedInput={focusedInput}
              startDate={startDate}
              endDate={endDate}
              hideKeyboardShortcutsPanel
            />
          </div>
        )}
      </Measure>
    );
  }
}

DayPickerRangeControllerWrapper.propTypes = propTypes;
DayPickerRangeControllerWrapper.defaultProps = defaultProps;

export default DayPickerRangeControllerWrapper;
