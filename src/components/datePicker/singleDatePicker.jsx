import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { DayPickerSingleDateController } from 'react-dates';
import { isMobile, isTablet } from 'utils/mediaQueries';
import './datepicker.scss';

const STATE_FORMAT = 'YYYY-MM-DD';

/**
 * A datepicker that selects a single date.
 *
 * @class SingleDatePickerWrapper
 * @extends {Component}
 */
class SingleDatePickerWrapper extends Component {
  static propTypes = {
    onDateChange: PropTypes.func,
    value: PropTypes.string,
    withPortal: PropTypes.bool,
    isOutsideRange: PropTypes.func,
    isDayHighlighted: PropTypes.func,
    renderCalendarInfo: PropTypes.func,
  };

  static defaultProps = {
    withPortal: true,
  };

  constructor(props) {
    super(props);

    this.state = {
      focused: false,
    };
  }

  /**
   * Called when a date is selected from the datePicker
   */
  onDateChange = date => {
    // Format the moment date before calling the props handler.
    const formatedDate = date ? moment(date).format(STATE_FORMAT) : null;
    this.props.onDateChange(formatedDate);
  };

  /**
   * Called when the focus on the input changes
   */
  onFocusChange = ({ focused }) => {
    if (focused !== this.state.focused) {
      this.setState({ focused });
    }
  };

  render() {
    const { focused } = this.state;
    const { value, withPortal, isOutsideRange, isDayHighlighted, renderCalendarInfo } = this.props;

    // Transform the value into a moment obj.
    const date = value ? moment(value, STATE_FORMAT) : null;

    return (
      <DayPickerSingleDateController
        isOutsideRange={isOutsideRange}
        initialVisibleMonth={() => date || moment()}
        withPortal={withPortal}
        date={date}
        isDayHighlighted={isDayHighlighted}
        focused={focused}
        numberOfMonths={isMobile() || isTablet() ? 1 : 2}
        onDateChange={this.onDateChange}
        onFocusChange={this.onFocusChange}
        hideKeyboardShortcutsPanel
        renderCalendarInfo={renderCalendarInfo}
      />
    );
  }
}

export default SingleDatePickerWrapper;
