import React, { Component } from 'react';
import PropTypes from 'prop-types';
import moment from 'moment';
import { DateRangePicker } from 'react-dates';
import { Label } from 'semantic-ui-react';
import { isMobile, isTablet } from 'utils/mediaQueries';
import './datepicker.scss';

const DISPLAY_FORMAT = 'DD-MM-YYYY';
const STATE_FORMAT = 'YYYY-MM-DD';
/**
 * A date-range picker that takes a starting date and the number
 * of nights and displays the start-date, end-date accordingly.
 *
 * @class DateRangePickerWrapper
 * @extends {Component}
 */
class DateRangePickerWrapper extends Component {
  static propTypes = {
    onDatesChange: PropTypes.func,
    checkIn: PropTypes.string,
    nights: PropTypes.number,
    startDateId: PropTypes.string.isRequired,
    startDatePlaceholderText: PropTypes.string,
    endDatePlaceholderText: PropTypes.string,
    enableOutsideDays: PropTypes.bool,
    isDayBlocked: PropTypes.func,
    isOutsideRange: PropTypes.func,
    hasError: PropTypes.bool,
    errorMessage: PropTypes.string,
    showClearDates: PropTypes.bool,
    fluid: PropTypes.bool,
    from: PropTypes.string,
    to: PropTypes.string
  };

  static defaultProps = {
    startDatePlaceholderText: 'Check In',
    endDatePlaceholderText: 'Check Out',
    enableOutsideDays: false,
    showClearDates: false,
    fluid: false
  };

  constructor(props) {
    super(props);
    this.state = {
      focusedInput: null
    };
  }

  /**
   * Called when any of the startDate / endDate changes
   *
   * @param {Object} { startDate, nights } The start date formated, and the number of nights
   */
  onDatesChange = ({ startDate, endDate }) => {
    const nights = startDate && endDate ? endDate.diff(startDate, 'days') : null;
    const checkIn = startDate ? moment(startDate).format(STATE_FORMAT) : null;

    this.props.onDatesChange({ checkIn, nights });
  };

  /**
   * Called when we focus to an input ( start/end ).
   *
   * @param {any} focusedInput The input that has focus
   */
  onFocusChange = focusedInput => {
    // As the DatePicker calls the onFocusChange for
    // different reasons we want to make sure the focusedInput
    // has changed before changing the state.
    if (focusedInput !== this.state.focusedInput) {
      this.setState({ focusedInput });
    }
  };

  render() {
    const { focusedInput } = this.state;
    const {
      checkIn,
      nights,
      startDateId,
      startDatePlaceholderText,
      endDatePlaceholderText,
      enableOutsideDays,
      isDayBlocked,
      isOutsideRange,
      hasError,
      errorMessage,
      showClearDates,
      fluid,
      from,
      to
    } = this.props;

    const extraProps = {
      ...(typeof isDayBlocked !== 'undefined' ? { isDayBlocked } : undefined),
      ...(typeof isOutsideRange !== 'undefined' ? { isOutsideRange } : undefined)
    };
    const nowMoment = moment();

    let visibleMoment = nowMoment.clone();
    if (from && to) {
      /**
       * Property provides operation-dates from -> to, define visible-moment using
       * the operation-dates.
       */
      const fromMoment = moment(from).year(nowMoment.year());
      const toMoment = moment(to).year(nowMoment.year());

      if (nowMoment.isBefore(fromMoment)) {
        visibleMoment = fromMoment.clone();
      } else if (nowMoment.isAfter(toMoment)) {
        visibleMoment = fromMoment.clone().add('1', 'year');
      }
    }

    const startDate = checkIn ? moment(checkIn, STATE_FORMAT) : null;
    const endDate = checkIn ? moment(checkIn, STATE_FORMAT).add(nights, 'days') : null;

    return (
      <div className={`qb-date-range-picker ${fluid ? 'is-fluid' : ''}`}>
        <DateRangePicker
          displayFormat={DISPLAY_FORMAT}
          initialVisibleMonth={() => startDate || visibleMoment}
          startDatePlaceholderText={startDatePlaceholderText}
          endDatePlaceholderText={endDatePlaceholderText}
          startDateId={startDateId}
          {...extraProps}
          enableOutsideDays={enableOutsideDays}
          numberOfMonths={isMobile() || isTablet() ? 1 : 2}
          withPortal
          withFullScreenPortal={isMobile() || isTablet()}
          onDatesChange={this.onDatesChange}
          onFocusChange={this.onFocusChange}
          focusedInput={focusedInput}
          hideKeyboardShortcutsPanel
          startDate={startDate}
          showClearDates={showClearDates}
          endDate={endDate}
        />
        {hasError && (
          <Label
            pointing
            basic
            color="yellow"
            content={errorMessage || 'Please select a valid date'}
          />
        )}
      </div>
    );
  }
}

export default DateRangePickerWrapper;
