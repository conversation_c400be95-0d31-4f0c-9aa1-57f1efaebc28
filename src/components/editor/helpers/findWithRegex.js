/**
 * Takes a regex, the contentBlock and our callback and will invoke the callback
 * with the ranges for all the matches found
 *
 * @param {RegExp} regex
 * @param {Object} contentBlock
 * @param {function} callback
 */
const findWithRegex = (regex, contentBlock, callback) => {
  const text = contentBlock.getText();
  let matchArr;
  let start;
  let end;

  while ((matchArr = regex.exec(text)) !== null) {
    start = matchArr.index;
    end = start + matchArr[0].length;
    callback(start, end);
  }
};

export default findWithRegex;
