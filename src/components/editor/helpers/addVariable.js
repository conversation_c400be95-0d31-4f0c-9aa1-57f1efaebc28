import { Modifier, EditorState } from 'draft-js';
import { EntityMutability, EditorChangeType, ENTITY_TYPE } from '../constants';

const addVariable = (editorState, selection) => {

  const contentState = editorState.getCurrentContent();

  const contentStateWithEntity = contentState.createEntity(
    ENTITY_TYPE.MUSTACHE_AUTOCOMPLETE,
    EntityMutability.mutable
  );

  const entityKey = contentStateWithEntity.getLastCreatedEntityKey();

  const newContentState = Modifier.replaceText(
    contentStateWithEntity,
    selection,
    ' ',
    null,
    entityKey
  );

  const newEditorState = EditorState.push(
    editorState,
    newContentState,
    EditorChangeType.insertCharacters
  );

  return EditorState.forceSelection(newEditorState, newContentState.getSelectionAfter());
};

export default addVariable;
