import { Modifier, EditorState } from 'draft-js';
import { EntityMutability, EditorChangeType, ENTITY_TYPE } from '../constants';

const addShortcode = (editorState, displayText, value, label) => {
  const contentState = editorState.getCurrentContent();
  const selectionState = editorState.getSelection();

  const contentStateWithEntity = contentState.createEntity(
    ENTITY_TYPE.SHORTCODE,
    EntityMutability.immutable,
    {
      originalText: value,
      label,
    }
  );

  const entityKey = contentStateWithEntity.getLastCreatedEntityKey();

  const contentStateWithText = Modifier.replaceText(
    contentStateWithEntity,
    selectionState,
    displayText,
    undefined,
    entityKey
  );

  const newEditorState = EditorState.push(
    editorState,
    contentStateWithText,
    EditorChangeType.insertCharacters
  );

  const editorStateToUpdate = EditorState.forceSelection(
    newEditorState,
    contentStateWithText.getSelectionAfter()
  );

  return editorStateToUpdate;
};

export default addShortcode;
