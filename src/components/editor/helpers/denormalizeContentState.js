/* eslint react/jsx-filename-extension: 0 */
import React from 'react';
import { convertToHTML } from 'draft-convert';
import { ENTITY_TYPE } from '../constants';

const ESCAPED_CHARACTERS = {
  '&': '&amp;',
  '<': '&lt;',
  '>': '&gt;',
  '"': '&quot;',
  "'": '&#x27;',
  '`': '&#x60;',
};


/**
 * Used to avoid HTML decoration of Layout blocks which cause
 * invalid template formats.
 */
function isLayoutBlock(text) {
  if (text.indexOf('{{#>') === 0 || text.indexOf('{{/') === 0) {
    return true;
  } else {
    return false;
  }
}

const denormalizeContentState = (contentState, rawText = false) =>
  convertToHTML({
    blockToHTML: block => {
      if (rawText || isLayoutBlock(block.text)) {
        // @HEADS_UP
        // Undocumented API in draft-convert
        // We can return an object instead of an element if we want to manually
        // declare our block's html
        // It resolves to `${start}${empty}${end}`
        return {
          start: '',
          end: '',
          empty: block.text,
        };
      }

      if (block.type === 'unstyled') {
        if (block.text === ' ' || block.text === '') {
          return <br />;
        }
        return <p />;
      }

      return undefined;
    },
    entityToHTML: (entity, originalText) => {
      switch (entity.type) {
        case ENTITY_TYPE.MUSTACHE:
        case ENTITY_TYPE.PARTIAL: {
          // Un - escape the text within handlebars statements
          const unescapedText = Object.keys(ESCAPED_CHARACTERS).reduce(
            (text, key) => text.replace(new RegExp(ESCAPED_CHARACTERS[key], 'g'), key),
            originalText
          );

          return unescapedText;
        }

        case ENTITY_TYPE.SHORTCODE: {
          // Un - escape the text within handlebars statements
          const unescapedText = Object.keys(ESCAPED_CHARACTERS).reduce(
            (text, key) => text.replace(new RegExp(ESCAPED_CHARACTERS[key], 'g'), key),
            entity.data.originalText
          );

          return unescapedText || entity.data.originalText;
        }

        case ENTITY_TYPE.LINK: {
          return <a href={entity.data.url}>{originalText}</a>;
        }

        default: {
          return originalText;
        }
      }
    },
  })(contentState);

export default denormalizeContentState;
