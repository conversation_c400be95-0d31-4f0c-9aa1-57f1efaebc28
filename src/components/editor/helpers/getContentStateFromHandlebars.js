import { EditorState, CompositeDecorator } from 'draft-js';
import { convertFromHTML } from 'draft-convert';
// import { get } from 'lodash';
import { ENTITY_TYPE, EntityMutability } from '../constants';
import {
  // MustacheVariableDecorator,
  AutocompleteDecorator,
  HandlebarsDecorator,
  ShortcodeDecorator,
  LinkDecorator
} from '../decoratorComponents';

// const MUSTACHE_STATEMENT_REGEX = /{{\s*[\w.[\]]+\s*}}/g;
const HANDLEBARS_ENTITY_REGEX = /{{{?[\s]*[\w\W]+?\s*}}}?/g;
const ENTER_CHAR_REGEX = /(↵|\n)/g;

/**
 * Get the mustache strategy
 */
// const getMustacheStrategy = getEditorState => (block, callback) => {
//   const editorState = getEditorState();

//   if (editorState) {
//     // Decorate text with entity type mustache
//     block.findEntityRanges(
//       val => {
//         const entityKey = val.getEntity();
//         if (!entityKey) {
//           return false;
//         }
//         const contentState = editorState.getCurrentContent();
//         return contentState.getEntity(entityKey).getType() === ENTITY_TYPE.MUSTACHE;
//       },
//       (start, end) => callback(start, end)
//     );
//   }
// };

const getStrategyByEntityType = (getEditorState, enityType) => (block, callback) => {
  const editorState = getEditorState();

  if (editorState) {
    // Decorate text with entity type mustache
    block.findEntityRanges(
      val => {
        const entityKey = val.getEntity();
        if (!entityKey) {
          return false;
        }
        const contentState = editorState.getCurrentContent();
        return contentState.getEntity(entityKey).getType() === enityType;
      },
      (start, end) => callback(start, end)
    );
  }
};

/**
 * Create decorators for our editor text
 */
const createCompositeDecorator = ({ getEditorState, onChange, setReadOnly, options }) =>
  new CompositeDecorator([
    {
      strategy: getStrategyByEntityType(getEditorState, ENTITY_TYPE.MUSTACHE_AUTOCOMPLETE),
      component: AutocompleteDecorator,
      props: {
        options,
        onChange,
        getEditorState,
        setReadOnly,
      },
    },
    {
      strategy: getStrategyByEntityType(getEditorState, ENTITY_TYPE.PARTIAL),
      component: HandlebarsDecorator,
      props: {
        options,
        onChange,
        getEditorState,
        setReadOnly,
      },
    },
    {
      strategy: getStrategyByEntityType(getEditorState, ENTITY_TYPE.SHORTCODE),
      component: ShortcodeDecorator,
      props: {
        options,
        onChange,
        getEditorState,
        setReadOnly,
      },
    },
    {
      strategy: getStrategyByEntityType(getEditorState, ENTITY_TYPE.LINK),
      component: LinkDecorator,
      props: {
        options,
        onChange,
        getEditorState,
        setReadOnly,
      },
    },
  ]);

/**
 * Takes the template and params and transforms it to editor state
 * along with decorators and entities
 */
export default function getStateFromTemplate({
  handlebarsTemplate,
  options,
  getEditorState,
  onChange,
  setReadOnly,
}) {
  if (!handlebarsTemplate) {
    return EditorState.createEmpty(
      createCompositeDecorator({
        setReadOnly,
        getEditorState,
        options,
        onChange,
      })
    );
  }

  // Remove newlines
  // Newlines in html text should not affect the content.
  const template = handlebarsTemplate.replace(ENTER_CHAR_REGEX, '');

  const contentWithEntities = convertFromHTML({
    textToEntity: (text, createEntity) => {
      const result = [];

      // text.replace(MUSTACHE_STATEMENT_REGEX, (match, offset) => {
      //   const textToReplace =
      //     get(variablesObject, match.replace('{{', '').replace('}}', '')) || 'NOT_FOUND';

      //   const entityKey = createEntity(ENTITY_TYPE.MUSTACHE, EntityMutability.immutable, {
      //     originalText: match,
      //   });
      //   result.push({
      //     entity: entityKey,
      //     offset,
      //     length: match.length,
      //     result: textToReplace,
      //   });
      // });

      text.replace(HANDLEBARS_ENTITY_REGEX, (match, offset) => {
        // const MustacheRegexInstance = new RegExp(MUSTACHE_STATEMENT_REGEX);
        // const isMustache = MustacheRegexInstance.test(match);

        // if (isMustache) {
        //   return;
        // }

        const entityKey = createEntity(ENTITY_TYPE.PARTIAL, EntityMutability.immutable, {
          originalText: match,
        });

        result.push({
          entity: entityKey,
          offset,
          length: match.length,
          result: match,
        });
      });

      if (options && options.length && options.length > 0) {
        options.forEach(option =>
          text.replace(option.handlebars, (match, offset) => {
            const entityKey = createEntity(ENTITY_TYPE.SHORTCODE, EntityMutability.immutable, {
              originalText: match,
              label: option.category,
            });

            result.push({
              entity: entityKey,
              offset,
              length: match.length,
              result: option.name,
            });
          })
        );
      }

      return result;
    },
    /* eslint consistent-return: 0 */
    htmlToEntity: (nodeName, node, createEntity) => {
      if (nodeName === 'a') {
        return createEntity(ENTITY_TYPE.LINK, EntityMutability.mutable, { url: node.href });
      }
    },
  })(template);

  const editorState = EditorState.createWithContent(
    contentWithEntities,
    createCompositeDecorator({
      setReadOnly,
      getEditorState,
      options,
      onChange,
    })
  );

  return editorState;
}
