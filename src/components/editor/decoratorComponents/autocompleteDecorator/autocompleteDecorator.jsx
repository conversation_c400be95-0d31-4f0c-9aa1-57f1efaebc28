import React, { Component } from 'react';
import { shape, string, func, arrayOf, node } from 'prop-types';
import { Modifier, EditorState } from 'draft-js';
import { Dropdown, Input } from 'semantic-ui-react';
import { find } from 'lodash';
import { ENTITY_TYPE } from '../../constants';

import './autocompleteDecorator.scss';

class HandlebarsBlock extends Component {
  static propTypes = {
    getEditorState: func,
    onChange: func,
    setReadOnly: func,
    children: node,
    options: arrayOf(
      shape({
        handlebars: string,
        name: string,
        category: string,
      })
    ),
  };

  constructor(props) {
    super(props);
    this.state = {
      isOpen: true,
    };
  }

  componentWillMount() {
    this.props.setReadOnly(true);
  }

  componentDidMount() {
    this.inputRef.focus();
  }

  componentWillUnmount() {
    this.props.setReadOnly(false);
  }

  onChange = (e, { value }) => {
    const { options } = this.props;
    const selectedOption = find(options, { text: value });

    const { getEditorState, onChange } = this.props;
    const editorState = getEditorState();
    const selectionState = editorState.getSelection();

    const anchorOffset = selectionState.getAnchorOffset();
    const currentContent = editorState.getCurrentContent();

    const partialSelection = selectionState.merge({
      anchorOffset: anchorOffset - 1,
      focusOffset: anchorOffset,
    });

    const contentStateWithEntity = currentContent.createEntity(ENTITY_TYPE.MUSTACHE, 'IMMUTABLE', {
      originalText: `{{${value}}}`,
    });

    const entityKey = contentStateWithEntity.getLastCreatedEntityKey();

    const contentStateWithText = Modifier.replaceText(
      contentStateWithEntity,
      partialSelection,
      selectedOption.value,
      undefined,
      entityKey
    );

    const newEditorState = EditorState.push(editorState, contentStateWithText, 'insert-characters');

    const editorStateToUpdate = EditorState.forceSelection(
      newEditorState,
      contentStateWithText.getSelectionAfter()
    );

    this.setState({ isOpen: false }, () => {
      onChange(editorStateToUpdate);
      this.props.setReadOnly(false);
    });
  };

  handleChange = (e, { value }) => {
    this.setState({ value });
  };

  handleClose = () => {
    const { getEditorState, onChange } = this.props;
    const editorState = getEditorState();

    const newEditorState = EditorState.undo(editorState);

    this.setState({ isOpen: false }, () => {
      onChange(newEditorState);
      this.props.setReadOnly(false);
    });
  };

  render() {
    const { isOpen } = this.state;
    const { children, options } = this.props;

    // const optionsMap = options.reduce((result, option) => {
    //   const currentOptions = [];
    //   const previousOption = result[result.length - 1];

    //   if (!previousOption || option.category !== previousOption.category) {
    //     currentOptions.push({
    //       key: option.category,
    //       text: option.category,
    //       disabled: true,
    //       className: 'header',
    //     });
    //   }

    //   currentOptions.push({
    //     key: option.name,
    //     text: option.name,
    //     value: option.name,
    //     category: option.category,
    //   });

    //   return result.concat(currentOptions);
    // }, []);

    return (
      <span>
        {!isOpen && <span style={{ background: '#457fff' }}>{children}</span>}
        {isOpen && (
          <Dropdown
            selectOnBlur={false}
            selectOnNavigation={false}
            open
            onClose={this.handleClose}
            className="qb-content-editor-autocomplete"
            closeOnChange={false}
            closeOnBlur={false}
            disabled={!isOpen}
            placeholder={null}
            value={null}
            icon={null}
            floating
            searchInput={
              <Input
                ref={inputNode => {
                  this.inputRef = inputNode;
                }}
                className="qb-content-editor-autocomplete-input"
                transparent
                style={{
                  background: '#dbe6fe',
                  color: '#4680ff',
                }}
              />
            }
            search
            options={options.map(({ name }) => ({ text: name, value: name }))}
            onChange={this.onChange}
          />
        )}
      </span>
    );
  }
}

export default HandlebarsBlock;
