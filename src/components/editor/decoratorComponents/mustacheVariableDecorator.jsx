import React from 'react';

import { Breadcrumb, Popup } from 'semantic-ui-react';
import { capitalizeFirstLetter } from 'utils/stringUtils';

const MustacheVariableDecorator = ({ children, contentState, entityKey }) => {
  const entity = entityKey ? contentState.getEntity(entityKey) : undefined;
  const { originalText = '' } = entity ? entity.getData() : {};
  const sections = originalText
    .replace('{{', '')
    .replace('}}', '')
    .split('.')
    .filter(text => text !== '.')
    .map(capitalizeFirstLetter);

  return (
    <Popup
      trigger={
        <span
          style={{
            borderBottom: '3px solid #457fff'
          }}
        >
          {children}
        </span>
      }
      content={<Breadcrumb sections={sections} divider=">" />}
    />
  );
};

export default MustacheVariableDecorator;
