import React from 'react';
import PropTypes from 'prop-types';

const getLabel = (contentState, entityKey) => {
  const entity = entityKey ? contentState.getEntity(entityKey) : undefined;
  const { label = '' } = entity ? entity.getData() : {};

  return label;
};

const ShortcodeDecorator = ({ children, contentState, entityKey }) => (
  <span
    style={{
      border: '1px dashed #feb851',
      background: '#fff0db',
    }}
  >
    <span style={{ color: '#a5adb6' }}>{`${getLabel(contentState, entityKey)}: `}</span>
    {children}
  </span>
);

ShortcodeDecorator.propTypes = {
  children: PropTypes.node,
  contentState: PropTypes.shape({
    getEntity: PropTypes.func,
  }),
  entityKey: PropTypes.string,
};

export default ShortcodeDecorator;
