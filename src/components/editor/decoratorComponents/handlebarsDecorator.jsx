import React from 'react';
import { node } from 'prop-types';
import { Popup } from 'semantic-ui-react';

const HandlebarsDecorator = ({ children }) => (
  <Popup
    trigger={
      <span
        contentEditable={false}
        style={{
          border: '1px dashed #457fff',
          background: '#dbe6fe',
        }}
      >
        {children}
      </span>
    }
    content={'Handlebars Block'}
  />
);

HandlebarsDecorator.propTypes = {
  children: node,
};

export default HandlebarsDecorator;
