import React from 'react';
import PropTypes from 'prop-types';

import { Popup } from 'semantic-ui-react';

const getUrl = (contentState, entityKey) => {
  const entity = entityKey ? contentState.getEntity(entityKey) : undefined;
  const { url = '' } = entity ? entity.getData() : {};

  return url;
};

const LinkDecorator = ({ children, contentState, entityKey }) => {
  const url = getUrl(contentState, entityKey);
  return <Popup trigger={<a href={url}>{children}</a>} content={url} />;
};

LinkDecorator.propTypes = {
  children: PropTypes.node,
  contentState: PropTypes.shape({
    getEntity: PropTypes.func,
  }),
  entityKey: PropTypes.string,
};

export default LinkDecorator;
