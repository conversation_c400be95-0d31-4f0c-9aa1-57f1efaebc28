# Handlebars Editor

An editor component specifically designed to edit handlebars files.

### Props

| Name | Type | Description |
|---|---|---|
| text | string | The handlebars text to edit |
| variables | object | An object with all the available variables that can be inserted or are already in the handlebars text |
| partials | object | An object with all the available partials |
| onSave | func | Returns the edited handlebars text |

### Resources for draft-js

[Get started and basic concepts explained](http://reactrocket.com/post/getting-started-with-draft-js/)
