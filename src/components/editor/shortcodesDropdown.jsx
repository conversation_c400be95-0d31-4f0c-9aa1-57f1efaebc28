import React, { Component } from 'react';
import { string, arrayOf, shape, element, func } from 'prop-types';
import { Dropdown } from 'semantic-ui-react';
import { groupBy } from 'lodash';
import addShortcode from './helpers/addShortcode';

const mapOption = option => ({
  ...option,
  value: option.name,
  text: option.name,
  key: `${option.name}-${option.category}`,
});

const mapOptions = options => options.map(mapOption);

const getGroupedOptions = options => {
  const groupObj = groupBy(options, 'category');

  return Object.keys(groupObj).map(key => ({
    name: key,
    options: groupObj[key],
  }));
};

class ShortcodesDropdown extends Component {
  static propTypes = {
    trigger: element.isRequired,
    onChange: func,
    onClose: func,
    editorState: string,
    options: arrayOf(
      shape({
        handlebars: string,
        name: string,
        category: string,
      })
    ),
  };

  constructor(props) {
    super(props);
    this.state = {};
  }

  handleChange = (e, props) => {
    this.props.onChange(
      addShortcode(this.props.editorState, props.name, props.handlebars, props.category)
    );
  };

  renderGroup = group => [
    <Dropdown.Header icon="tag" color="blue" content={group.name} />,
    ...group.options.map(option => <Dropdown.Item {...option} onClick={this.handleChange} />),
    <Dropdown.Divider key={`${group.name}-divider`} />,
  ];

  render() {
    const { options = [], trigger, onClose } = this.props;

    const optionsGroups = getGroupedOptions(mapOptions(options));

    return (
      <Dropdown
        scrolling
        onClose={onClose}
        pointing="right"
        trigger={trigger}
        icon={null}
        onChange={this.handleChange}
      >
        <Dropdown.Menu>
          {optionsGroups.reduce((result, group) => result.concat(this.renderGroup(group)), [])}
        </Dropdown.Menu>
      </Dropdown>
    );
  }
}

export default ShortcodesDropdown;
