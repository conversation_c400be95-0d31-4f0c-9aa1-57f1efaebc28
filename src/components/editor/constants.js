const EditorChangeType = {
  adjustDepth: 'adjust-depth',
  applyEntity: 'apply-entity',
  backspace<PERSON>haracter: 'backspace-character',
  changeBlockData: 'change-block-data',
  changeBlockType: 'change-block-type',
  changeInlineStyle: 'change-inline-style',
  moveBlock: 'move-block',
  delete<PERSON><PERSON>cter: 'delete-character',
  insertCharacters: 'insert-characters',
  insertFragment: 'insert-fragment',
  redo: 'redo',
  removeRange: 'remove-range',
  spellcheckChange: 'spellcheck-change',
  splitBlock: 'split-block',
  undo: 'undo',
};

const EntityMutability = {
  immutable: 'IMMUTABLE',
  mutable: 'MUTABLE',
  segmented: 'SEGMENTED',
};

const ENTITY_TYPE = {
  PARTIAL_BLOCK: 'PartialBlock',
  PARTIAL: 'Partial',
  MUSTACHE: 'Mustache',
  MUSTACHE_AUTOCOMPLETE: 'MustacheAutocomplete',
  CONTENT: 'Content',
  SHORTCODE: 'Shortcode',
  LINK: 'Link',
};

export { EntityMutability, EditorChangeType, ENTITY_TYPE };

export default EditorChangeType;
