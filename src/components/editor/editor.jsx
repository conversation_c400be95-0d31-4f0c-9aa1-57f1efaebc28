/* global document */
import React, { Component } from 'react';
import { string, arrayOf, oneOf, bool } from 'prop-types';
import { Segment, Grid, Button, Transition } from 'semantic-ui-react';
import { Editor } from 'draft-js';
import DraftOffsetKey from 'draft-js/lib/DraftOffsetKey';

import InlineToolbar from './inlineToolbar';
import addVariable from './helpers/addVariable';
import getContentStateFromHandlebars from './helpers/getContentStateFromHandlebars';
import { denormalizeContentState } from './helpers/index';
import ShortcodesDropdown from './shortcodesDropdown';

import './editor.scss';

function customBlockStyleFn(contentBlock) {
  const type = contentBlock.getType();
  if (type === 'unstyled') {
    return 'qb-editor-unstyled-block';
  }

  return undefined;
}

class HandlebarsEditor extends Component {
  static propTypes = {
    hideInlineToolbar: bool,
    size: oneOf(['small', 'medium']),
    template: string,
    placeholder: string,
    options: arrayOf({
      category: string,
      handlebars: string,
      name: string,
    }),
  };

  constructor(props) {
    super(props);

    const { template = '', options = [] } = props;

    const editorState = getContentStateFromHandlebars({
      handlebarsTemplate: template,
      options,
      getEditorState: this.getEditorState,
      onChange: this.onChange,
      setReadOnly: this.setReadOnly,
    });

    this.isMenuOpen = false;

    this.state = {
      // The empty draft-js editor state
      editorState,
      // We use this flag when we want to display content inside the editor
      // (e.g. inputs, dropdowns) that are not editable
      isReadOnly: false,
      // Track if the editor has received focus
      isFocused: false,
      isEditorContainerFocused: true,
      html: '',
    };
  }

  componentDidMount() {
    const { template = '', options = [] } = this.props;

    const editorState = getContentStateFromHandlebars({
      handlebarsTemplate: template,
      options,
      getEditorState: this.getEditorState,
      onChange: this.onChange,
      setReadOnly: this.setReadOnly,
    });

    this.onChange(editorState);
  }

  componentWillReceiveProps({ template, options }) {
    if (
      (template && template !== this.props.template) ||
      (template && options && options !== this.props.options)
    ) {
      const editorState = getContentStateFromHandlebars({
        handlebarsTemplate: template,
        options,
        getEditorState: this.getEditorState,
        onChange: this.onChange,
        setReadOnly: this.setReadOnly,
      });

      this.onChange(editorState);
    }
  }

  // Handle the state updates
  onChange = editorState => {
    this.updateSidebarTop(editorState);
    this.setState({ editorState });
  };

  // Focus on the editor
  onFocus = () => {
    const focus = this.editorNode ? this.editorNode.focus : () => {};
    this.setState({ isFocused: true }, focus);
  };

  onBlur = () => {
    this.setState({ isFocused: false });
  };

  onSave = () => {
    const { editorState } = this.state;

    const contentState = editorState.getCurrentContent();

    const html = denormalizeContentState(contentState);

    this.setState({ html });
  };

  getHTML = ({ rawText } = { rawText: false }) => {
    const { editorState } = this.state;

    const contentState = editorState.getCurrentContent();

    const html = denormalizeContentState(contentState, rawText);

    return html;
  };

  // Set the editor's readOnly flag
  setReadOnly = isReadOnly => this.setState({ isReadOnly }, () => !isReadOnly && this.onFocus());

  // Get the editor state
  getEditorState = () => (this.state ? this.state.editorState : undefined);

  handleEditorRef = node => {
    this.editorNode = node;
  };

  updateSidebarTop = editorState => {
    if (!this.editorNode) {
      return;
    }
    const selection = editorState.getSelection();
    const currentContent = editorState.getCurrentContent();
    const currentBlock = currentContent.getBlockForKey(selection.getStartKey());

    const offsetKey = DraftOffsetKey.encode(currentBlock.getKey(), 0, 0);

    // Note: need to wait on tick to make sure the DOM node has been create by Draft.js
    setTimeout(() => {
      const node = document.querySelectorAll(`[data-offset-key="${offsetKey}"]`)[0];
      if (!node) {
        return;
      }
      const currentElementTop = node.getBoundingClientRect().top;
      const editor = this.editorNode.editor;
      const editorTop = editor.getBoundingClientRect().top;

      this.setState({
        sidebarTop: currentElementTop - editorTop,
      });
    }, 0);
  };

  // handleBeforeInput = input => {
  //   if (input === '{') {
  //     const { editorState } = this.state;
  //     /* Get the selection */
  //     const selection = editorState.getSelection();

  //     /* Get the current block */
  //     const currentBlock = editorState.getCurrentContent().getBlockForKey(selection.getStartKey());

  //     // Get the block text
  //     const blockText = currentBlock.getText();

  //     // Get the anchorOffset
  //     const anchorOffset = selection.getAnchorOffset();

  //     // Get the latest character of our input
  //     const lastChar = blockText.charAt(anchorOffset - 1);

  //     const preLastChar = blockText.charAt(anchorOffset - 2);

  //     // User types two consecutive brackets {{ after return or space so we turn the block to a variable block
  //     if (lastChar === '{' && (preLastChar === ' ' || preLastChar === '\n' || !preLastChar)) {
  //       // Get the focusOffset
  //       const focusOffset = selection.getFocusOffset();

  //       const selectionToReplace = selection.merge({
  //         anchorOffset: anchorOffset - 1,
  //         focusOffset,
  //       });

  //       this.onChange(addVariable(editorState, selectionToReplace));

  //       // Return true to let the editor know the event was handled
  //       return true;
  //     }
  //   }

  //   // Return false to let the editor handle the event
  //   return false;
  // };

  // Sync set this to true to continue displaying the dropdown
  handleMenuOpen = () => {
    this.isMenuOpen = true;
  };

  handleMenuClose = () => {
    this.isMenuOpen = false;
  };

  handleInsertVariable = () => {
    const { editorState } = this.state;

    const selection = editorState.getSelection();

    this.onChange(addVariable(editorState, selection));
  };

  render() {
    const { sidebarTop = {}, editorState, isFocused } = this.state;
    const { size = 'medium', hideInlineToolbar } = this.props;
    const hasText = editorState.getCurrentContent().hasText();

    const sidebarStyles = {
      position: 'absolute',
      top: sidebarTop,
    };

    return (
      <div
        onBlur={this.onBlur}
        ref={node => {
          this.editorContainerNode = node;
        }}
      >
        <Segment
          className={`qb-editor-wrapper is-${size} ${hasText ? 'has-text' : ''} ${
            isFocused ? 'is-focused' : ''
          }`}
        >
          <Grid>
            <Grid.Row>
              <Grid.Column width={15}>
                <div
                  className={`qb-editor-container is-${size} ${hasText ? 'has-text' : ''}`}
                  onFocus={this.onFocus}
                >
                  <Editor
                    readOnly={this.state.isReadOnly}
                    editorState={this.state.editorState}
                    blockStyleFn={customBlockStyleFn}
                    onChange={this.onChange}
                    placeholder={this.props.placeholder || 'Enter some text...'}
                    // handleBeforeInput={this.handleBeforeInput}
                    ref={this.handleEditorRef}
                  />
                </div>
              </Grid.Column>
              <Grid.Column width={1} textAlign="right" style={{ padding: '0 0 10px 0' }}>
                <div style={sidebarStyles}>
                  <Transition visible={isFocused || this.isMenuOpen} animation="scale">
                    <div onMouseDown={this.handleMenuOpen}>
                      <ShortcodesDropdown
                        onChange={this.onChange}
                        editorState={editorState}
                        options={this.props.options}
                        onClose={this.handleMenuClose}
                        trigger={
                          <Button
                            color="orange"
                            basic
                            size="tiny"
                            circular
                            icon={<div style={{ fontSize: '10px' }}>{'{...}'}</div>}
                          />
                        }
                      />
                    </div>
                  </Transition>
                </div>
              </Grid.Column>
              {/* <Grid.Column width={1} textAlign="right" style={{ padding: '0 0 10px 0' }}>
              <Popup
                style={{ padding: '0' }}
                content={
                  <Button.Group basic compact vertical>
                    <Button icon="content" content="Variable" onClick={this.handleInsertVariable} />
                    <Button icon="" content="Partial" />
                  </Button.Group>
                }
                on="click|focus"
                trigger={
                  <div style={sidebarStyles}>
                    <Transition visible={isEditorContainerFocused} animation="scale">
                      <Button
                        basic
                        circular
                        icon={<div style={{ fontSize: '12px' }}>{'{...}'}</div>}
                      />
                    </Transition>
                  </div>
                }
              />
            </Grid.Column> */}
            </Grid.Row>
          </Grid>
          {!hideInlineToolbar && (
            <InlineToolbar
              editor={this.editorContainerNode}
              onChange={this.onChange}
              editorState={editorState}
            />
          )}
        </Segment>
      </div>
    );
  }
}

export default HandlebarsEditor;
