/* global window document */
import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Portal, Button, Transition, Dropdown, Icon, Label, Input } from 'semantic-ui-react';
import { throttle } from 'lodash';
import { getVisibleSelectionRect, RichUtils, EditorState } from 'draft-js';
import { ENTITY_TYPE, EntityMutability } from './constants';

const BLOCK_TYPES = [
  { text: 'H1', value: 'header-one' },
  { text: 'H2', value: 'header-two' },
  { text: 'H3', value: 'header-three' },
  // { text: 'H4', value: 'header-four' },
  // { text: 'H5', value: 'header-five' },
  // { text: 'H6', value: 'header-six' },
  // { text: 'Blockquote', value: 'blockquote' },
  // { text: 'UL', value: 'unordered-list-item' },
  // { text: 'OL', value: 'ordered-list-item' },
];

const INLINE_STYLES = [
  { label: 'Bold', style: 'BOLD', icon: 'bold' },
  { label: 'Italic', style: 'ITALIC', icon: 'italic' },
  { label: 'Underline', style: 'UNDERLINE', icon: 'underline' },
];

const getUrlValue = nextProps => {
  const { editorState } = nextProps;
  const selection = editorState.getSelection();
  if (selection) {
    const contentState = editorState.getCurrentContent();
    const startKey = editorState.getSelection().getStartKey();
    const startOffset = editorState.getSelection().getStartOffset();
    const blockWithLinkAtBeginning = contentState.getBlockForKey(startKey);
    const linkKey = blockWithLinkAtBeginning.getEntityAt(startOffset);
    let url = '';
    if (linkKey) {
      const linkInstance = contentState.getEntity(linkKey);
      const isLinkType = linkInstance.getType() === ENTITY_TYPE.LINK;
      if (isLinkType) {
        url = linkInstance.getData().url;
      }
    }
    if (url) {
      return url;
    }
  }

  return null;
};

class InlineToolbar extends Component {
  static propTypes = {
    editorState: PropTypes.shape({
      getSelection: PropTypes.func,
    }),
    editor: PropTypes.node,
    onChange: PropTypes.func,
  };

  constructor(props) {
    super(props);

    this.onSelectionChange = throttle(this.onSelectionChange, 300);

    this.state = {
      position: {
        top: 0,
        left: 0,
      },
      visible: false,
      isUrlInputVisible: false,
      urlValue: getUrlValue(props),
      selection: null,
    };
  }

  componentWillReceiveProps(nextProps) {
    const nextEditorStateSelection = nextProps.editorState.getSelection();
    const nextSelectionStartKey = nextEditorStateSelection.getStartKey();
    const nextSelectionEndKey = nextEditorStateSelection.getEndKey();
    const nextSelectionStart = nextEditorStateSelection.getStartOffset();
    const nextSelectionEnd = nextEditorStateSelection.getEndOffset();

    const currentEditorStateselection = this.props.editorState.getSelection();
    const currentSelectionStart = currentEditorStateselection.getStartOffset();
    const currentSelectionEnd = currentEditorStateselection.getEndOffset();

    const shouldClose =
      Math.abs(nextSelectionStart - nextSelectionEnd) === 0 &&
      nextSelectionStartKey === nextSelectionEndKey;

    if (currentSelectionStart !== nextSelectionStart || currentSelectionEnd !== nextSelectionEnd) {
      this.onSelectionChange(shouldClose);
      // Update the url value if set
      const urlValue = getUrlValue(nextProps);

      if (urlValue) {
        this.setState({ urlValue });
      }
    }
  }

  onSelectionChange = shouldClose => {
    const editor = this.props.editor;

    if (shouldClose) {
      if (this.state.isUrlInputVisible && this.state.urlValue) {
        this.confirmLink();
      } else {
        this.setState({
          visible: false,
          isUrlInputVisible: false,
          urlValue: '',
        });
      }
    } else {
      setTimeout(() => {
        if (!editor) return;

        const toolbarHeight = 52; // this.toolbar.clientHeight;
        const toolbarWidth = 164.5; // this.toolbar.clientWidth;
        // const relativeRect = (editor || document.body).getBoundingClientRect();
        const selectionRect = getVisibleSelectionRect(window);
        let pointing = 'center';

        if (!selectionRect) return;

        const pageWidth = window.document.body.clientWidth;

        // Toolbar doesn't fit right
        if (pageWidth - selectionRect.right < (toolbarWidth + 5) / 2) {
          pointing = 'right';
        }

        // Toolbar doesn't fit left
        if (selectionRect.left < (toolbarWidth + 5) / 2) {
          pointing = 'left';
        }

        if (selectionRect.right - selectionRect.left > toolbarWidth) {
          pointing = 'center';
        }

        const position = {
          top: `${selectionRect.top - toolbarHeight}px`,
          left: {
            left: `${selectionRect.left + selectionRect.width / 2}px`,
            center: `${selectionRect.left + selectionRect.width / 2 - toolbarWidth / 2}px`,
            right: `${selectionRect.left + selectionRect.width / 2 - toolbarWidth}px`,
          }[pointing],
        };

        this.setState({
          position,
          visible: true,
          pointing,
          isUrlInputVisible: false,
          urlValue: '',
        });
      }, 0);
    }
  };

  handleToolbarRef = node => {
    this.setState({ toolbarNode: node });
  };

  toggleInlineStyle = inlineStyle => {
    this.props.onChange(RichUtils.toggleInlineStyle(this.props.editorState, inlineStyle));
  };

  toggleBlockType = (e, { value }) => {
    this.props.onChange(RichUtils.toggleBlockType(this.props.editorState, value));
  };

  handleUrlInputChange = (e, { value }) => {
    this.setState({ urlValue: value });
  };

  handleUrlInputKeyDown = e => {
    if (e.which === 13) {
      this.confirmLink(e);
    }
  };

  openLinkForm = () => {
    this.setState(
      { isUrlInputVisible: true, selection: this.props.editorState.getSelection() },
      () => {
        setTimeout(() => {
          if (this.urlInput) {
            this.urlInput.focus();
          }
        }, 50);
      }
    );
  };

  closeLinkForm = () => {
    this.setState({ isUrlInputVisible: false, urlValue: '' });
  };

  confirmLink = () => {
    const { urlValue, selection } = this.state;
    const { editorState, onChange } = this.props;

    const correctedUrl = /^http/.test(urlValue) ? urlValue : `http://${urlValue}`;

    if (selection && correctedUrl) {
      const contentState = editorState.getCurrentContent();
      const contentStateWithEntity = contentState.createEntity(
        ENTITY_TYPE.LINK,
        EntityMutability.mutable,
        {
          url: correctedUrl,
        }
      );

      const entityKey = contentStateWithEntity.getLastCreatedEntityKey();

      const editorStateWithEntity = EditorState.push(editorState, contentStateWithEntity);

      const editorStateWithLink = RichUtils.toggleLink(editorStateWithEntity, selection, entityKey);

      this.setState({ urlValue: '', isUrlInputVisible: false, visible: false }, () => {
        onChange(editorStateWithLink);
      });
    }
  };

  removeLink = () => {
    const { editorState, onChange } = this.props;

    const selection = editorState.getSelection();
    if (!selection.isCollapsed()) {
      this.setState({ urlValue: '' }, () => {
        onChange(RichUtils.toggleLink(editorState, selection, null));
      });
    }
  };

  render() {
    const { visible, position, pointing, isUrlInputVisible, urlValue } = this.state;
    const { editorState } = this.props;
    const isUrlButtonActive = getUrlValue(this.props);

    const currentStyle = editorState.getCurrentInlineStyle();

    const selection = editorState.getSelection();
    const blockType = editorState
      .getCurrentContent()
      .getBlockForKey(selection.getStartKey())
      .getType();

    const toolbarStyle = {
      position: 'fixed',
      zIndex: 150,
      padding: 0,
      ...position,
    };

    return (
      <div>
        <Portal open>
          <Transition visible={visible} animation="slide up" duration={200}>
            <div style={toolbarStyle}>
              <div ref={this.handleToolbarRef}>
                <Label
                  className={`qb-editor-toolbar pointing-${pointing}`}
                  as={Button.Group}
                  style={{ padding: 0, boxShadow: 'rgba(25, 17, 34, 0.1) 0px 10px 42px' }}
                  pointing="below"
                  basic
                >
                  {!isUrlInputVisible && (
                    <div>
                      <Dropdown
                        button
                        icon={null}
                        className="icon"
                        selectOnBlur={false}
                        onChange={this.toggleBlockType}
                        options={BLOCK_TYPES}
                        value={blockType}
                        trigger={<Icon name="header" />}
                      />
                      {INLINE_STYLES.map(style => {
                        const toggleInlineStyle = () => this.toggleInlineStyle(style.style);
                        const isActive = currentStyle.has(style.style);

                        return (
                          <Button
                            active={isActive}
                            icon={style.icon}
                            onClick={toggleInlineStyle}
                            style={{ margin: 0 }}
                          />
                        );
                      })}
                      <Button
                        active={isUrlButtonActive}
                        icon={isUrlButtonActive ? 'unlinkify' : 'linkify'}
                        onClick={isUrlButtonActive ? this.removeLink : this.openLinkForm}
                        style={{ margin: 0 }}
                      />
                    </div>
                  )}
                  {isUrlInputVisible && (
                    <Input
                      style={{ margin: '0' }}
                      ref={node => {
                        this.urlInput = node;
                      }}
                      size="large"
                      icon={{ name: 'cancel', link: true, onClick: this.closeLinkForm }}
                      placeholder="Paste or type a link"
                      value={urlValue}
                      onChange={this.handleUrlInputChange}
                      onKeyDown={this.handleUrlInputKeyDown}
                    />
                  )}
                </Label>
              </div>
            </div>
          </Transition>
        </Portal>
      </div>
    );
  }
}

export default InlineToolbar;
