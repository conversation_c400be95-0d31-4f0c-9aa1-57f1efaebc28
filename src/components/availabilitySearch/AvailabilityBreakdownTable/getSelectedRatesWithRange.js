import uuid from 'utils/uuid';
import { sortBy } from 'lodash';
import moment from 'moment';

const mapRateToOffer = rate => ({
  title: rate.room,
  description: rate.rate,
  rateId: rate.rateId,
  nights: rate.nights,
  children: rate.children,
  adults: rate.adults,
  infants: rate.infants,
  propertyCode: rate.propertyCode,
  currency: rate.currency,
  rooms: rate.rooms,
  rate: {
    id: rate.rateId,
    room: rate.accommodationCode,
    name: rate.rate,
    roomName: rate.room,
    currency: rate.currency
  },
  accommodation: [{ name: rate.room, code: rate.accommodationCode }],
  checkIn: rate.checkIn,
  releaseAt: rate.expireAt,
  officialRate: rate.officialRate,
  roomRate: rate.roomRate,
  discountRate: rate.discountRate,
  excludedCharges: rate.excludedCharges,
  cancellationExpiration: rate.cancellationExpiration,
  taxesRate: rate.taxesRate
});

const mapRatesToOffers = rates => rates.map(mapRateToOffer);

const getOfferDates = offer => ({
  start: moment(offer.checkIn),
  end: moment(offer.checkIn).add(offer.nights, 'days')
});

const offerOptionReducer = (offers, offer, index) => {
  let optionId = uuid();

  const offerDates = getOfferDates(offer);
  const prevOffer = offers[offers.length - 1];

  if (prevOffer) {
    const prevOfferDates = getOfferDates(prevOffer);

    if (prevOfferDates.end.isSame(offerDates.start)) {
      optionId = prevOffer.optionIds[0];
    }
  }

  return [...offers, { ...offer, optionIds: [optionId] }];
};

const getOffersWithOptions = offers => offers.reduce(offerOptionReducer, []);

// Take a list of offers and group together the offers that are consecutive.
const groupConsecutiveOffers = offers => {
  // Sort offers by date so that we dont look backwards
  const sortedOffersByCheckIn = sortBy(offers, ({ checkIn }) => moment(checkIn).unix());

  return getOffersWithOptions(sortedOffersByCheckIn);
};

const getSelectedRatesWithRange = (allRatesBreakdown, filters) => {
  const { nights, checkIn, ...pax } = filters;

  const rates = [];

  /**
   * [ -- RatesBreakdown -- ] -> [ -- DateRanges -- ]
   *
   * Loop through each rate selected days
   * After we find a dateRange push it to the rates with sum of price etc
   */
  allRatesBreakdown.forEach(rate => {
    const { days } = rate;

    const selectedDaysWithRange = days
      // Filter only the selected days
      .filter(({ isSelected }) => isSelected)
      // Categorize them in ranges
      .reduce((ranges, day) => {
        const prevRange = ranges[ranges.length - 1];

        const prevDay =
          prevRange && prevRange.checkIn
            ? moment(prevRange.checkIn).add(prevRange.nights - 1, 'day')
            : null;

        if (prevDay) {
          const currentDay = moment(day.date);

          const areConsecutiveDays = currentDay.diff(prevDay, 'days') === 1;

          if (areConsecutiveDays) {
            return ranges.map((range, index) => {
              if (index === ranges.length - 1) {
                return {
                  ...range,
                  nights: range.nights + 1,
                  roomRate: range.roomRate + day.roomRate,
                  officialRate: range.officialRate + day.officialRate,
                  discountRate: range.discountRate + day.discountRate
                };
              }
              return range;
            });
          }
          return ranges.concat({
            checkIn: day.date,
            nights: 1,
            officialRate: day.officialRate,
            discountRate: day.discountRate,
            roomRate: day.roomRate
          });
        }
        return [
          {
            checkIn: day.date,
            nights: 1,
            officialRate: day.officialRate,
            discountRate: day.discountRate,
            roomRate: day.roomRate
          }
        ];
      }, []);

    selectedDaysWithRange.forEach(range => {
      const { days, status, statusMessage, ...rateData } = rate;
      rates.push({
        ...range,
        officialRate: range.officialRate / range.nights,
        roomRate: range.roomRate / range.nights,
        discountRate: range.discountRate / range.nights,
        ...rateData,
        ...pax
      });
    });
  });

  return groupConsecutiveOffers(mapRatesToOffers(rates));
};

export default getSelectedRatesWithRange;
