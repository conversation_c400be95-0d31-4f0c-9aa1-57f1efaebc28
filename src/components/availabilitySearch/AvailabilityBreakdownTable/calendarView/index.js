import { compose } from 'react-apollo';

import { connect } from 'react-redux';
import selectAvailabilityBreakdownRange from 'state/actions/availability/selectAvailabilityBreakdownRange';

import getSortedRatesByAvailability from 'state/selectors/rates/getSortedRatesByAvailability';
import getRatesResultFilters from 'state/selectors/rates/getRatesResultFilters';

import moment from 'moment';

import CalendarView from './calendarView';

const CalendarViewStateContainer = connect(
  state => {
    const filters = getRatesResultFilters(state);
    const from = filters.checkIn;
    const to = moment(filters.checkIn)
      .add(filters.nights, 'days')
      .format();
    return {
      filters,
      isLoading: state.rates.ratesBreakdown.isLoading,
      events: getSortedRatesByAvailability(state),
      dateRange: {
        from,
        to,
      },
    };
  },
  dispatch => ({
    onSelectDateRange: ({ id, from, to, select }) => {
      dispatch(selectAvailabilityBreakdownRange({ id, from, to, select }));
    },
  })
);

export default compose(CalendarViewStateContainer)(CalendarView);
