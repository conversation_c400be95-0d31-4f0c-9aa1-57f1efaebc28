import React, { PureComponent } from 'react';
import { arrayOf, string, bool, number, shape, func } from 'prop-types';
import moment from 'moment';
import { Segment, Popup, Icon, List } from 'semantic-ui-react';
import { AutoSizer, Grid, ScrollSync } from 'react-virtualized';

import { find } from 'lodash';

import { scrollbarSize } from 'dom-helpers';

import currencyFormatter from 'currency-formatter';

import getSelectedRatesWithRange from './getSelectedRatesWithRange';

const getRangeOfDates = (start, end, key, arr = [start.startOf(key)]) => {
  if (start.isAfter(end)) throw new Error('start must precede end');

  const next = moment(start)
    .add(1, key)
    .startOf(key);

  if (next.isAfter(end, key)) return arr;

  return getRangeOfDates(next, end, key, arr.concat(next));
};

const getSelectedDaysFromSelection = ({ days, from, to, select = true }) => {
  const start = moment(from);
  const end = moment(to);

  return days.map(day => {
    let isSelected = day.isSelected;

    if (start.isSameOrBefore(moment(day.date)) && end.isSameOrAfter(moment(day.date))) {
      isSelected = select;
    }

    return {
      ...day,
      isSelected
    };
  });
};

const getColorByStatus = status =>
  ({
    AVAILABLE: '#fff',
    NOT_AVAILABLE: 'rgb(235, 236, 237)'
  }[status]);

const getNumberValue = (value = 0) => value / 100;

export default class AvailabilityBreakdownTable extends PureComponent {
  static propTypes = {
    onEditSelectedRates: func,
    /**
     * The events to display in the calendar
     */
    events: arrayOf(
      shape({
        rateId: string,
        rate: string,
        days: arrayOf(
          shape({
            date: string.isRequired,
            status: string,
            remaining: number,
            price: number,
            initial: number,
            discount: number,
            isSelected: bool
          })
        )
      })
    ),
    /**
     * The date range we want to display in the calendar
     */
    dateRange: shape({
      from: string.isRequired,
      to: string.isRequired
    }).isRequired
  };

  static defaultProps = {
    onEditSelectedRates: () => {}
  };

  constructor(props, context) {
    super(props, context);
    let rangeOfDates = [];
    const eventsLength = props.events ? props.events.length : 0;

    if (props.dateRange && props.dateRange.from && props.dateRange.to) {
      const start = moment(props.dateRange.from).add(-1, 'day');
      const end = moment(props.dateRange.to).add(-1, 'day');

      if (end.isAfter(start)) {
        rangeOfDates = getRangeOfDates(start, end, 'day');
      }
    }

    this.state = {
      events: props.events,
      columnWidth: 170,
      columnCount: rangeOfDates.length,
      dates: rangeOfDates,
      height: 700,
      overscanColumnCount: 5,
      overscanRowCount: 5,
      rowHeight: 100,
      rowCount: eventsLength,
      selectedCell: { x: '', y: '' },
      hoveredCell: { x: '', y: '' }
    };
  }

  componentWillReceiveProps(nextProps) {
    const { dateRange } = this.props;
    const currentFrom = dateRange.from;
    const currentTo = dateRange.to;

    const { from, to } = nextProps.dateRange;

    if (currentFrom !== from || currentTo !== to) {
      const start = moment(from).add(-1, 'day');
      const end = moment(to).add(-1, 'day');
      if (end.isAfter(start)) {
        const rangeOfDates = getRangeOfDates(start, end, 'day');

        this.setState({
          columnCount: rangeOfDates.length,
          dates: rangeOfDates
        });
      }
    }

    if (this.props.events !== nextProps.events) {
      const eventsLength = nextProps.events ? nextProps.events.length : 0;

      this.setState({
        rowCount: eventsLength
      });
    }
  }

  updateEvent = ({ id, from, to, select }) => {
    this.setState(
      state => ({
        events: state.events.map(event => {
          if (event.rateId === id) {
            return {
              ...event,
              days: getSelectedDaysFromSelection({ days: event.days, from, to, select })
            };
          }
          return event;
        })
      }),
      () =>
        this.props.onEditSelectedRates(
          getSelectedRatesWithRange(this.state.events, this.props.filters)
        )
    );
  };

  handleClick = (columnIndex, rowIndex) => {
    const { selectedCell, dates } = this.state;

    if (rowIndex === selectedCell.y) {
      const id = this.state.events[rowIndex].rateId;
      let from = '';
      let to = '';

      if (columnIndex === selectedCell.x) {
        from = dates[columnIndex];
        to = dates[columnIndex];
      } else if (columnIndex > selectedCell.x) {
        from = dates[selectedCell.x];
        to = dates[columnIndex];
      } else {
        from = dates[columnIndex];
        to = dates[selectedCell.x];
      }

      this.updateEvent({
        id,
        from: moment(from).format(),
        to: moment(to).format(),
        select: !selectedCell.prevSelected
      });

      this.setState({ selectedCell: { x: '', y: '', prevSelected: false } });
    } else {
      const { rateId: id, days } = this.state.events[rowIndex];
      const cellDate = dates[columnIndex];

      const { isSelected } = find(days, ({ date }) => moment(date).isSame(cellDate));

      this.updateEvent({
        id,
        from: moment(cellDate).format(),
        to: moment(cellDate).format(),
        select: !isSelected
      });
      this.setState({ selectedCell: { x: columnIndex, y: rowIndex, prevSelected: !!isSelected } });
    }
  };

  handleHover = (columnIndex, rowIndex) => {
    this.setState({ hoveredCell: { x: columnIndex, y: rowIndex } });
  };

  renderBodyCell({ columnIndex, key, rowIndex, style, selectedCell, hoveredCell }) {
    if (columnIndex < 1) {
      return null;
    }

    if (columnIndex === 0) {
      return this.renderLeftSideCell({
        columnIndex,
        key,
        rowIndex,
        style,
        selectedCell,
        hoveredCell
      });
    }

    return this.renderContentCell({
      columnIndex,
      key,
      rowIndex,
      style,
      selectedCell,
      hoveredCell
    });
  }

  /**
   * Render the header cells of the grid
   */
  renderHeaderCell = ({ columnIndex, key, style }) => {
    if (columnIndex < 1) {
      return (
        <div
          key={key}
          style={{
            ...style,
            background: '#F9FAFB',
            border: 'none',
            color: 'black',
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center'
          }}
        />
      );
    }

    const dateString = moment(this.state.dates[columnIndex]).format('DD/MM');
    const day = moment(this.state.dates[columnIndex]).format('dddd');

    return (
      <div
        key={key}
        style={{
          ...style,
          background: '#F9FAFB',
          border: 'none',
          color: 'black',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center',
          alignItems: 'center'
        }}
      >
        <div>{dateString}</div>
        <div>{day}</div>
      </div>
    );
  };

  /**
   * Render the left cell in the header
   */
  renderLeftHeaderCell = ({ key, style }) => <div key={key} style={style} />;

  /**
   * Render the basic content cell
   */
  renderContentCell({
    columnIndex,
    key,
    rowIndex,
    style,
    selectedCell = { x: '', y: '' },
    hoveredCell = { x: '', y: '' }
  }) {
    const { events } = this.state;
    const cellEvent = events[rowIndex];

    const { dates } = this.state;
    const currentDate = dates[columnIndex];

    let rate = find(cellEvent.days, ({ date }) => moment(date).isSame(currentDate));

    if (!rate) {
      rate = {};
    }

    const { isSelected, remaining, officialRate, status, discountRate } = rate;

    // True if the user is hovering left of the selected cell
    const isHoveringLeft = hoveredCell.x < selectedCell.x;

    // True if the row of this cell is hovered
    const isCellRowHovered = rowIndex === hoveredCell.y && rowIndex === selectedCell.y;

    // True if the column of this cell is hovered
    const isCellColumnHovered = isHoveringLeft
      ? columnIndex <= selectedCell.x && columnIndex >= hoveredCell.x
      : columnIndex >= selectedCell.x && columnIndex <= hoveredCell.x;

    // True if this cell is inside the range of the hovered and selected cell
    const isHoveredOverSelected = isCellRowHovered && isCellColumnHovered;

    const bgColor = selectedCell.prevSelected && isHoveredOverSelected ? '#fb617f' : '#4680ff';

    return (
      <div
        onClick={() => this.handleClick(columnIndex, rowIndex)}
        onMouseOver={() => this.handleHover(columnIndex, rowIndex)}
        key={key}
        style={{
          ...style,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          flexDirection: 'column',
          border: '0.5px solid rgba(239, 239, 239, 0.2)',
          background: getColorByStatus(status),
          color: isSelected || isHoveredOverSelected ? 'white' : 'black',
          cursor: 'pointer'
        }}
      >
        <div
          style={{
            position: 'absolute',
            top: '25%',
            left: '0',
            width: '100%',
            height: '50%',
            boxShadow:
              isSelected || isHoveredOverSelected
                ? '0 4px 16px rgba(50,50,93,.11), 0 1px 3px rgba(0,0,0,.08)'
                : 'none',
            background: isSelected || isHoveredOverSelected ? bgColor : ''
          }}
        />
        <List style={{ zIndex: '2', margin: 0 }}>
          <List.Item>
            {currencyFormatter.format(getNumberValue(officialRate - discountRate), {
              code: cellEvent.currency || 'EUR'
            })}
          </List.Item>
          <List.Item icon="hotel" content={`x ${remaining}`} />
        </List>
      </div>
    );
  }

  /**
   * Render the left sidebar of the calendar with the title of the event
   */
  renderLeftSideCell = ({ key, rowIndex, style }) => {
    const event = this.state.events[rowIndex] || { rate: 'Not Found' };
    const prevEvent = this.state.events[rowIndex - 1] || { rate: 'Not Found' };

    const shouldShowRoomName = event.room !== prevEvent.room;

    const { rate, status, statusMessage, room } = event;
    return (
      <div
        key={key}
        style={{
          ...style,
          display: 'flex',
          justifyContent: 'center',
          alignItems: 'center',
          background: '#F9FAFB',
          border: '0px',
          color: 'black',
          textAlign: 'center',
          padding: '10px'
        }}
      >
        {shouldShowRoomName && (
          <List>
            <List.Item header={room} content={rate} />
          </List>
        )}
        {!shouldShowRoomName ? rate : null}
        {status === 'NOT_AVAILABLE' && (
          <Popup
            inverted
            trigger={<Icon color="yellow" name="warning sign" />}
            content={statusMessage}
          />
        )}
      </div>
    );
  };

  render() {
    const {
      columnCount,
      columnWidth,
      height,
      overscanColumnCount,
      overscanRowCount,
      rowHeight,
      rowCount,
      selectedCell,
      hoveredCell
    } = this.state;

    const { events } = this.state;
    const hasEvents = !!(events && events.length && events.length > 0);

    return (
      <Segment basic>
        {hasEvents && (
          <ScrollSync>
            {({ onScroll, scrollLeft, scrollTop }) => (
              <div
                style={{
                  position: 'relative',
                  borderRadius: '4px',
                  boxShadow: '0 5px 20px rgba(25, 17, 34, 0.1)',
                  overflow: 'hidden',
                  background: 'rgb(249, 250, 251)'
                }}
              >
                <div>
                  <div
                    style={{
                      position: 'absolute',
                      left: 0,
                      top: 0
                    }}
                  >
                    <Grid
                      cellRenderer={this.renderLeftHeaderCell}
                      width={columnWidth}
                      height={rowHeight}
                      rowHeight={rowHeight}
                      columnWidth={columnWidth}
                      rowCount={1}
                      columnCount={1}
                    />
                  </div>
                  <div
                    className="calendar-column-left"
                    style={{
                      position: 'absolute',
                      left: 0,
                      zIndex: 2,
                      top: rowHeight
                    }}
                  >
                    <Grid
                      style={{ overflowY: 'hidden', outline: 'none' }}
                      overscanColumnCount={overscanColumnCount}
                      overscanRowCount={overscanRowCount}
                      cellRenderer={this.renderLeftSideCell}
                      columnWidth={columnWidth}
                      columnCount={1}
                      height={height - scrollbarSize()}
                      rowHeight={rowHeight}
                      rowCount={rowCount}
                      scrollTop={scrollTop}
                      width={columnWidth}
                    />
                  </div>
                  <div>
                    <AutoSizer disableHeight>
                      {({ width }) => (
                        <div>
                          <div
                            style={{
                              height: rowHeight,
                              width: width - scrollbarSize()
                            }}
                          >
                            <Grid
                              style={{ overflowX: 'hidden', outline: 'none' }}
                              columnWidth={columnWidth}
                              columnCount={columnCount}
                              height={rowHeight}
                              overscanColumnCount={overscanColumnCount}
                              cellRenderer={this.renderHeaderCell}
                              rowHeight={rowHeight}
                              rowCount={1}
                              scrollLeft={scrollLeft}
                              width={width - scrollbarSize()}
                            />
                          </div>
                          <div
                            style={{
                              height,
                              width,
                              outline: 'none'
                            }}
                          >
                            <Grid
                              style={{
                                outline: 'none'
                              }}
                              columnWidth={columnWidth}
                              columnCount={columnCount}
                              height={height}
                              onScroll={onScroll}
                              overscanColumnCount={overscanColumnCount}
                              overscanRowCount={overscanRowCount}
                              cellRenderer={args =>
                                this.renderBodyCell({ ...args, selectedCell, hoveredCell })
                              }
                              rowHeight={rowHeight}
                              rowCount={rowCount}
                              width={width}
                            />
                          </div>
                        </div>
                      )}
                    </AutoSizer>
                  </div>
                </div>
              </div>
            )}
          </ScrollSync>
        )}
      </Segment>
    );
  }
}
