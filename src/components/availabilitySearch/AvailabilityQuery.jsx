import { graphql } from 'react-apollo';
import getAvailability from 'queries/getAvailability.gql';
import getAvailabilityBreakdown from 'queries/getAvailabilityBreakdown.gql';
import getGroupAvailability from 'queries/getGroupAvailability.gql';
import { VIEW_TYPE } from './constants';

export const withAvailability = graphql(getAvailability, {
  props: ({ data, ownProps }) => ({
    loading: data.loading,
    rates: data.availability
      ? data.availability.map(rate => ({ ...rate, ...ownProps.variables }))
      : undefined,
    error: data.error
  }),
  options: ({ variables }) => {
    const {
      propertyCode,
      checkIn,
      nights,
      adults,
      children,
      infants,
      rooms,
      accommodationCode,
      country,
      board
    } = variables;

    return {
      variables: {
        propertyCode,
        checkIn,
        nights,
        adults,
        children,
        infants,
        rooms,
        accommodationCode,
        country,
        board
      }
    };
  },
  skip: ({ variables }) =>
    variables.view !== VIEW_TYPE.MULTIPLE_ROOMS ||
    !variables.propertyCode ||
    !variables.checkIn ||
    variables.nights < 1 ||
    variables.adults < 1 ||
    variables.infants < 0 ||
    variables.rooms < 1
});

export const withGroupAvailability = graphql(getGroupAvailability, {
  props: ({ data, ownProps }) => ({
    loading: data.loading,
    rates: data.rates ? data.rates.map(rate => ({ ...rate, ...ownProps.variables })) : undefined,
    error: data.error
  }),
  options: ({ variables }) => ({
    variables: {
      propertyCode: variables.propertyCode,
      checkIn: variables.checkIn,
      nights: variables.nights,
      country: variables.country
    }
  }),
  skip: ({ variables }) =>
    variables.view !== VIEW_TYPE.GROUPED_ROOMS ||
    !variables.propertyCode ||
    variables.nights < 0 ||
    variables.adults < 1
});

export const withAvailabilityBreakdown = graphql(getAvailabilityBreakdown, {
  props: ({ data }) => ({
    loading: data.loading,
    rates: data.rates,
    error: data.error
  }),
  options: ({ variables }) => ({ variables }),
  skip: ({ variables }) =>
    variables.view !== VIEW_TYPE.BREAKDOWN ||
    !variables.propertyCode ||
    !variables.checkIn ||
    variables.nights < 1 ||
    variables.adults < 1 ||
    variables.infants < 0 ||
    variables.rooms < 1
});
