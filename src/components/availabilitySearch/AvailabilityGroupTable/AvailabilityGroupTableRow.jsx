import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { <PERSON><PERSON>ield, Checkbox, Grid, Loader, Dimmer } from 'semantic-ui-react';
import AutohighlightInput from 'components/roomPricingForm/AutohighlightInput';
import CurrencyIcon from 'components/currencyIcon';
import {
  calculatePercentageFromValues,
  calculatePercentageValue,
  calculateRates
} from 'utils/calculationUtils';
import { DatePicker } from 'components/datePicker';
import { isInclusivelyAfterDay, isSameDay } from 'react-dates';
import moment from 'moment';

import { throttle } from 'lodash';

import getAvailability from 'queries/getAvailability.gql';
import { withApollo } from 'react-apollo';

import RoomNumberInput from './RoomNumberInput';

import './tableRow.scss';

class TableRow extends Component {
  static propTypes = {
    size: PropTypes.oneOf(['small', 'medium', 'large']),
    onChange: PropTypes.func,
    rate: PropTypes.shape({
      id: PropTypes.string,
      currency: PropTypes.string,
      idPrefix: PropTypes.string,
      officialRate: PropTypes.number,
      discountRate: PropTypes.number,
      rate: PropTypes.string,
      size: PropTypes.string,
      isSelected: PropTypes.bool,
      expireAt: PropTypes.string,
      checkIn: PropTypes.string,
      nights: PropTypes.number
    })
  };

  static defaultProps = {
    currency: 'EUR',
    idPrefix: 'id'
  };

  constructor(props) {
    super(props);

    this.state = {
      isLoading: false
    };

    // We throttle the field change call so that we don't always call the API
    this.handleFieldChange = throttle(this.handleFieldChange, 3000);
  }

  onChangeReleaseDate = date => {
    const { rate } = this.props;
    this.props.onChange({
      ...rate,
      expireAt: date
    });
  };

  getAvailability = rate => {
    const { client } = this.props;

    const {
      propertyCode,
      checkIn,
      nights,
      maxPax,
      rooms,
      accommodationCode,
      country,
      rateId,
      board
    } = rate;

    const variables = {
      propertyCode,
      checkIn,
      nights,
      adults: maxPax,
      children: 0,
      infants: 0,
      rooms,
      accommodationCode,
      country,
      rateId,
      board
    };

    this.setState({ isLoading: true });

    client
      .query({
        query: getAvailability,
        variables
      })
      .then(({ data }) => {
        let selectedRate =
          data &&
          data.availability &&
          data.availability.reduce((result, currentRate) => {
            if (currentRate.rateId === rate.rateId) {
              return currentRate;
            }
            return result;
          }, {});

        // backend availability does not provide adults
        selectedRate = { ...selectedRate, adults: maxPax, id: selectedRate.rateId };

        this.props.onChange({
          ...rate,
          ...selectedRate,
          adults: maxPax
        });

        this.setState({ isLoading: false });
      });
  };

  getFloatValue = (value = 0) => (value / 100).toFixed(2);

  getIntValue = (value = '') => parseInt(parseFloat(value) * 100, 10);

  toggleChecked = e => {
    e.preventDefault();
    const { rate } = this.props;

    const updatedRate = {
      ...rate,
      isSelected: !rate.isSelected
    };
    this.props.onChange(updatedRate);

    this.getAvailability(updatedRate);
  };

  handleChange = (e, { name, value }) => {
    let newName = name;
    let priceValue = value;
    if (name === 'roomRate') {
      priceValue = this.getIntValue(value);
    } else if (name === 'totalRoomRate') {
      priceValue = this.getIntValue(value) / this.props.rate.nights;
      newName = 'roomRate';
    } else if (name === 'officialRate') {
      priceValue = this.getIntValue(value);
    } else if (name === 'discountRate') {
      priceValue = value;
    }

    const { rate } = this.props;

    const newRates = calculateRates(rate, { name: newName, value: priceValue });

    this.props.onChange({
      ...rate,
      ...newRates
    });
  };

  handleFieldChange = ({ name, value }) => {
    const updatedRate = {
      ...this.props.rate,
      [name]: value
    };

    this.props.onChange(updatedRate);

    // Update the pricing if the rooms change
    if (name === 'rooms' && this.props.rate.isSelected) {
      this.getAvailability(updatedRate);
    }
  };

  render() {
    const { isLoading } = this.state;
    const { rate: availabilityRate, size } = this.props;
    const {
      currency,
      nights,
      checkIn,
      rateId,
      rate,
      rooms = 0,
      officialRate,
      discountRate,
      isSelected,
      expireAt
    } = availabilityRate;

    const identifier = `${rateId}-${rate}`;
    const dateInputId = `${identifier}-date`;
    const officialRateInputId = `${identifier}-official`;
    const priceInputId = `${identifier}-price`;
    const discountInputId = `${identifier}-discount`;
    const finalPriceInputId = `${identifier}-final`;

    const currencyIcon = <CurrencyIcon currency={currency} />;

    const columnWidths = {
      checkbox: 16,
      releaseAt: 16,
      priceInput: 16,
      rooms: 16
    };

    if (size === 'large' || size === 'medium') {
      columnWidths.checkbox = 8;
      columnWidths.releaseAt = 8;
      columnWidths.priceInput = 4;
    }

    return (
      <Grid.Row>
        <Grid.Column width={columnWidths.checkbox}>
          <Checkbox label={rate} checked={isSelected} onChange={this.toggleChecked} />
        </Grid.Column>
        <Grid.Column
          width={columnWidths.releaseAt}
          textAlign={size === 'small' ? 'right' : undefined}
        >
          <FormField
            type="date"
            name="release_at"
            className="qb-input qb-input-datepicker"
            control={DatePicker}
            inline={size === 'small'}
            fluid={size !== 'small'}
            label={'Release At'}
            isOutsideRange={day => {
              // Check if the given day is after or exactly today
              const isDayAfterToday =
                isInclusivelyAfterDay(day, moment()) || isSameDay(day, moment());
              // Check if the given day is before or exactly the check-in date
              const isDayBeforeCheckIn =
                !isInclusivelyAfterDay(day, moment(checkIn)) || isSameDay(day, moment(checkIn));

              return !(isDayAfterToday && isDayBeforeCheckIn);
            }}
            showClearDate
            id={dateInputId}
            onDateChange={this.onChangeReleaseDate}
            value={expireAt}
          />
        </Grid.Column>
        <Grid.Column width={columnWidths.rooms} textAlign={size === 'small' ? 'right' : undefined}>
          <RoomNumberInput onChange={this.handleFieldChange} value={rooms} />
        </Grid.Column>
        <Grid.Column width={16} style={{ zIndex: 0, position: 'relative' }}>
          <Dimmer.Dimmable as={Grid}>
            <Dimmer active={!isSelected || isLoading} inverted>
              {isLoading && <Loader active />}
            </Dimmer>
            <Grid.Row>
              <Grid.Column
                width={columnWidths.priceInput}
                textAlign={size === 'small' ? 'right' : undefined}
              >
                <FormField
                  fluid={size !== 'small'}
                  label={
                    <label
                      htmlFor={officialRateInputId}
                      style={{ marginRight: '5px', color: 'black' }}
                    >
                      Official Rate
                    </label>
                  }
                  id={officialRateInputId}
                  type="number"
                  name="officialRate"
                  onChange={this.handleChange}
                  control={AutohighlightInput}
                  className={`qb-input qb-input-colored qb-input-colored-disabled ${
                    discountRate ? 'is-disabled' : ''
                  }`}
                  icon={currencyIcon}
                  iconPosition="left"
                  value={parseFloat(this.getFloatValue(officialRate))}
                />
              </Grid.Column>
              <Grid.Column
                width={columnWidths.priceInput}
                textAlign={size === 'small' ? 'right' : undefined}
              >
                <FormField
                  inline={size === 'small'}
                  fluid={size !== 'small'}
                  label={
                    <label htmlFor={discountInputId} style={{ marginRight: '5px' }}>
                      Discount
                    </label>
                  }
                  control={AutohighlightInput}
                  type="number"
                  step="1"
                  className="qb-input qb-input-colored qb-input-colored-red"
                  id={discountInputId}
                  icon="percent"
                  name="discountRate"
                  iconPosition="left"
                  value={parseFloat(calculatePercentageFromValues(discountRate, officialRate))}
                  onChange={(e, { value, name }) =>
                    this.handleChange(e, {
                      value: calculatePercentageValue(value, officialRate),
                      name
                    })
                  }
                />
              </Grid.Column>
              <Grid.Column
                width={columnWidths.priceInput}
                textAlign={size === 'small' ? 'right' : undefined}
              >
                <div className="qb-input-average-rate-container">
                  <FormField
                    inline={size === 'small'}
                    fluid={size !== 'small'}
                    id={priceInputId}
                    label={
                      <label htmlFor={priceInputId} style={{ marginRight: '5px' }}>
                        Avg Rate
                      </label>
                    }
                    control={AutohighlightInput}
                    step="10"
                    type="number"
                    className="qb-input qb-input-colored qb-input-colored-orange"
                    icon={currencyIcon}
                    iconPosition="left"
                    name="roomRate"
                    value={parseFloat(this.getFloatValue(officialRate - discountRate))}
                    onChange={this.handleChange}
                  />
                </div>
              </Grid.Column>
              <Grid.Column
                width={columnWidths.priceInput}
                textAlign={size === 'small' ? 'right' : undefined}
              >
                <FormField
                  inline={size === 'small'}
                  fluid={size !== 'small'}
                  control={AutohighlightInput}
                  type="number"
                  step="10"
                  label={
                    <label htmlFor={finalPriceInputId} style={{ marginRight: '5px' }}>
                      Total
                    </label>
                  }
                  className="qb-input qb-input-colored qb-input-colored-blue"
                  id={finalPriceInputId}
                  icon={currencyIcon}
                  iconPosition="left"
                  name="totalRoomRate"
                  value={parseFloat(this.getFloatValue((officialRate - discountRate) * nights))}
                  onChange={this.handleChange}
                />
              </Grid.Column>
            </Grid.Row>
          </Dimmer.Dimmable>
        </Grid.Column>
      </Grid.Row>
    );
  }
}

export default withApollo(TableRow);
