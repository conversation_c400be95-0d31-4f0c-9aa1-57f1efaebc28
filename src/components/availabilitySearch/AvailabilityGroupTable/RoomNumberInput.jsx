import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { FormInput, Transition, Icon } from 'semantic-ui-react';
import { getNounFormByNumber } from 'utils/stringUtils';

class RoomNumberInput extends Component {
  static propTypes = {
    defaultValue: PropTypes.number,
    max: PropTypes.number,
    value: PropTypes.number,
    onChange: PropTypes.func,
    name: PropTypes.string
  };

  static defaultProps = {
    defaultValue: 1,
    max: 5,
    name: 'rooms'
  };

  constructor(props) {
    super(props);

    this.state = {
      value: props.defaultValue
    };
  }

  handleSearch = () => {
    const { onChange, name } = this.props;
    const { value } = this.state;

    onChange({ name, value });
  };

  handleChange = (e, { value }) => this.setState({ value: parseInt(value, 10) });

  render() {
    const { value } = this.state;
    const { max, name, value: rooms } = this.props;

    return (
      <div>
        <FormInput
          size="small"
          action={
            value === rooms
              ? null
              : {
                  style: {
                    borderRadius: '0 .28571429rem .28571429rem 0'
                  },
                  content: value === rooms ? '' : 'Update',
                  icon: value === rooms ? '' : 'refresh',
                  color: 'teal',
                  disabled: value === rooms,
                  onClick: this.handleSearch
                }
          }
          onChange={this.handleChange}
          label={<span style={{ marginRight: '5px' }}>Rooms</span>}
          value={value}
          name={name}
          icon="hotel"
          iconPosition="left"
          min={1}
          max={max}
          type="number"
        />
        <Transition visible={false || rooms !== value} animation="fade" duration={500}>
          <span style={{ color: '#feb64b', marginTop: '5px' }}>
            <Icon name="warning sign" />
            {`Price is applied for ${getNounFormByNumber(rooms, {
              singular: 'room',
              plural: 'rooms'
            })}`}
          </span>
        </Transition>
      </div>
    );
  }
}

export default RoomNumberInput;
