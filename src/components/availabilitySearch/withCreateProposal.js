import { connect } from 'react-redux';

import initializeProposal from 'state/actions/proposals/initializeProposal';
import { getUserOperatorId, getUserHasPermissionInAnyProperty } from 'state/selectors/user';

const withCreateProposal = connect(
  state => ({
    operatorId: getUserOperatorId(state),
    shouldShowWaitingList: getUserHasPermissionInAnyProperty(state, 'waiting-list')
  }),
  dispatch => ({
    createProposal: proposal =>
      dispatch(
        initializeProposal(
          {
            proposal
          },
          {
            shouldCheckForPersistedProposal: false
          }
        )
      )
  })
);

export default withCreateProposal;
