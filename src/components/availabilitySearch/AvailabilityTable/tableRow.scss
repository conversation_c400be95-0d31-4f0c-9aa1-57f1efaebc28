/** Policy Pricing Colored Inputs */

.qb-input {
  .ui.icon.input .ui.icon {
    line-height: 35px;
  }

  &.qb-input-datepicker {
    display: flex;
    justify-content: flex-end;
    align-items: center;

    > label {
      margin-right: 5px;
    }
  }

  &.qb-input-colored {
    &.qb-input-colored-blue {
      .icon i {
        color: #4680ff;
        opacity: 1;
      }

      .ui.input input {
        background: #dbe6fe;
        color: #4680ff;
        border: 1px solid #dbe6fe;

        &:focus {
          border: 1px solid #4680ff;
        }
      }
    }

    &.qb-input-colored-orange {
      .icon i {
        color: #feb64b;
        opacity: 1;
        font-weight: 400;
      }

      .ui.input input {
        background: #fff0db;
        color: #feb64b;
        border: 1px solid #fff0db;

        &:focus {
          border: 1px solid #feb64b;
        }
      }
    }

    &.qb-input-colored-red {
      .icon i {
        color: #fb617f;
        opacity: 1;
      }

      .ui.input input {
        background: #fedfe5;
        color: #fb617f;
        border: 1px solid #fedfe5;

        &:focus {
          border: 1px solid #fb617f;
        }
      }
    }

    &.qb-input-colored-disabled {
      color: #fb617f;

      &.is-disabled {
        text-decoration: line-through;

        .ui.disabled.input {
          opacity: 1 !important;

          input {
            width: 100%;
            opacity: 1 !important;
          }
        }
      }

      .icon i {
        opacity: 1;
        color: #4680ff;
      }

      .ui.input input {
        background: #f3f3f3;
        border: 1px solid #f3f3f3;
        color: #4680ff;

        &:focus {
          border-color: #dbdbdb;
        }
      }
    }
  }
}
