import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { Header, Grid } from 'semantic-ui-react';

class AvailabilityTableHeader extends Component {
  static propTypes = {
    room: PropTypes.string,
    remaining: PropTypes.number,
    size: PropTypes.string
  };

  shouldComponentUpdate(nextProps) {
    return this.props !== nextProps;
  }

  render() {
    const { size } = this.props;
    const isExtended = size === 'large';

    return (
      <Grid.Row style={{ background: '#F9FAFB', borderRadius: '.28571429rem .28571429rem 0 0' }}>
        <Grid.Column width="16" style={{ borderBottom: '0' }}>
          <Header as="h3">{`${this.props.room}`}</Header>
        </Grid.Column>
        <Grid.Column width={5}>
          <span>{`(${this.props.remaining} Available)`}</span>
        </Grid.Column>
        {isExtended && <Grid.Column width={3}>Release at</Grid.Column>}
        {isExtended && <Grid.Column width={2}>Official Rate</Grid.Column>}
        {isExtended && <Grid.Column width={2}>Discount</Grid.Column>}
        {isExtended && <Grid.Column width={2}>Avg Rate</Grid.Column>}
        {isExtended && <Grid.Column width={2}>Total</Grid.Column>}
      </Grid.Row>
    );
  }
}

export default AvailabilityTableHeader;
