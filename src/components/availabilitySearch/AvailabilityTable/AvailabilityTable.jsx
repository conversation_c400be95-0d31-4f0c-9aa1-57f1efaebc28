import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Grid, Segment } from 'semantic-ui-react';
import Measure from 'react-measure';
import AvailabilityTableRow from './AvailabilityTableRow';
import AvailabilityTableHeader from './AvailabilityTableHeader';
import mapAvailabbilityToGroupedRates from './state/mapAvailabbilityToGroupedRates';
import './table.scss';

const getSelectedRates = ratesGroups =>
  Object.keys(ratesGroups).reduce(
    (rates, key) => rates.concat(ratesGroups[key].filter(({ isSelected }) => isSelected)),
    []
  );

class AvailabilityTable extends Component {
  static propTypes = {
    /* eslint react/forbid-prop-types: 0 */
    rates: PropTypes.array,
    overwriteOfficialRate: PropTypes.bool,
    size: PropTypes.number,
    row: PropTypes.func,
    onSelectedRatesChange: PropTypes.func
  };

  constructor(props) {
    super(props);

    this.state = {
      rates: props.rates ? mapAvailabbilityToGroupedRates(props.rates) : [],
      dimensions: {
        width: -1,
        height: -1
      }
    };
  }

  componentWillReceiveProps = nextProps => {
    if (nextProps.rates !== this.props.rates) {
      this.setState({ rates: mapAvailabbilityToGroupedRates(nextProps.rates) });
    }
  };

  updateRate = rate => {
    this.setState(
      state => ({
        rates: {
          ...state.rates,
          [rate.accommodationCode]: state.rates[rate.accommodationCode].map(curRate => {
            if (curRate.rateId === rate.rateId) {
              return rate;
            }
            return curRate;
          })
        }
      }),
      () => {
        if (this.props.onSelectedRatesChange) {
          this.props.onSelectedRatesChange(getSelectedRates(this.state.rates));
        }
      }
    );
  };

  handleResize = ({ bounds: { width } }) => {
    let boundSize = 'small';

    if (width >= 1010) {
      boundSize = 'large';
    } else if (width >= 500) {
      boundSize = 'medium';
    }

    this.setState(({ size }) => (size !== boundSize ? { size: boundSize } : undefined));
  };

  renderAvailabilityTableRows = (rates = []) => {
    const { row, overwriteOfficialRate } = this.props;
    const { size } = this.state;

    return rates.map(rate => {
      const rowProps = {
        onChange: this.updateRate,
        size,
        overwriteOfficialRate,
        key: rate.rateId,
        rate
      };

      return row ? row(rowProps) : <AvailabilityTableRow {...rowProps} />;
    });
  };

  render() {
    const { size } = this.props;
    const { rates: groupedRates } = this.state;

    if (!groupedRates) return <div />;

    return (
      <Measure bounds onResize={this.handleResize}>
        {({ measureRef }) => (
          <div ref={measureRef}>
            {Object.keys(groupedRates).map(key => {
              const rates = groupedRates[key] || [];
              const { room, remaining } = rates[0];

              return (
                <Grid key={key} as={Segment} divided="vertically" className="qb-rate-table">
                  <AvailabilityTableHeader room={room} remaining={remaining} size={size} />
                  {this.renderAvailabilityTableRows(rates)}
                </Grid>
              );
            })}
          </div>
        )}
      </Measure>
    );
  }
}

export default AvailabilityTable;
