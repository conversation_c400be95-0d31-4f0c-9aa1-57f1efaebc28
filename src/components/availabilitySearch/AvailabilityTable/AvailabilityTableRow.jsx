import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { FormField, Checkbox, Grid } from 'semantic-ui-react';
import AutohighlightInput from 'components/roomPricingForm/AutohighlightInput';
import CurrencyIcon from 'components/currencyIcon';
import {
  calculatePercentageFromValues,
  calculatePercentageValue,
  calculateRates
} from 'utils/calculationUtils';
import { DatePicker } from 'components/datePicker';
import { isInclusivelyAfterDay, isSameDay } from 'react-dates';
import moment from 'moment';

import './tableRow.scss';

export default class TableRow extends Component {
  static propTypes = {
    size: PropTypes.oneOf(['small', 'medium', 'large']),
    overwriteOfficialRate: PropTypes.bool,
    onChange: PropTypes.func,
    rate: PropTypes.shape({
      rateId: PropTypes.string,
      currency: PropTypes.string,
      idPrefix: PropTypes.string,
      officialRate: PropTypes.number,
      discountRate: PropTypes.number,
      rate: PropTypes.string,
      size: PropTypes.string,
      isSelected: PropTypes.bool,
      expireAt: PropTypes.string,
      checkIn: PropTypes.string,
      nights: PropTypes.number
    })
  };

  static defaultProps = {
    currency: 'EUR',
    idPrefix: 'id'
  };

  onChangeReleaseDate = date => {
    const { rate } = this.props;
    this.props.onChange({
      ...rate,
      expireAt: date
    });
  };

  getFloatValue = (value = 0) => (value / 100).toFixed(2);

  getIntValue = (value = '') => parseInt(parseFloat(value) * 100, 10);

  toggleChecked = e => {
    e.preventDefault();
    const { rate } = this.props;
    this.props.onChange({
      ...rate,
      isSelected: !rate.isSelected
    });
  };

  handleChange = (e, { name, value }) => {
    let newName = name;
    let priceValue = value;
    if (name === 'roomRate') {
      priceValue = this.getIntValue(value);
    } else if (name === 'totalRoomRate') {
      priceValue = this.getIntValue(value) / this.props.rate.nights;
      newName = 'roomRate';
    } else if (name === 'officialRate') {
      priceValue = this.getIntValue(value);
    } else if (name === 'discountRate') {
      priceValue = value;
    }

    const { rate } = this.props;

    const newRates = calculateRates(rate, { name: newName, value: priceValue });

    this.props.onChange({
      ...rate,
      ...newRates
    });
  };

  render() {
    const { rate: availabilityRate, size, overwriteOfficialRate } = this.props;
    const {
      currency,
      officialRate,
      discountRate,
      nights,
      checkIn,
      rateId,
      rate,
      isSelected,
      expireAt
    } = availabilityRate;

    const identifier = `${rateId}-${rate}`;
    const dateInputId = `${identifier}-date`;
    const officialRateInputId = `${identifier}-official`;
    const priceInputId = `${identifier}-price`;
    const discountInputId = `${identifier}-discount`;
    const finalPriceInputId = `${identifier}-final`;

    const currencyIcon = <CurrencyIcon currency={currency} />;
    const shouldShowLabel = true;
    const columnWidths = {
      checkbox: 16,
      releaseAt: 16,
      priceInput: 16
    };

    if (size === 'large') {
      columnWidths.checkbox = 5;
      columnWidths.releaseAt = 3;
      columnWidths.priceInput = 2;
    } else if (size === 'medium') {
      columnWidths.checkbox = 8;
      columnWidths.releaseAt = 8;
      columnWidths.priceInput = 4;
    }

    return (
      <Grid.Row>
        <Grid.Column width={columnWidths.checkbox}>
          <Checkbox label={rate} checked={isSelected} onChange={this.toggleChecked} />
        </Grid.Column>
        <Grid.Column
          width={columnWidths.releaseAt}
          textAlign={size === 'small' ? 'right' : undefined}
        >
          <FormField
            type="date"
            name="release_at"
            control={DatePicker}
            inline={size === 'small'}
            fluid={size !== 'small'}
            label={shouldShowLabel ? 'Release At' : undefined}
            isOutsideRange={day => {
              // Check if the given day is after or exactly today
              const isDayAfterToday =
                isInclusivelyAfterDay(day, moment()) || isSameDay(day, moment());
              // Check if the given day is before or exactly the check-in date
              const isDayBeforeCheckIn =
                !isInclusivelyAfterDay(day, moment(checkIn)) || isSameDay(day, moment(checkIn));

              return !(isDayAfterToday && isDayBeforeCheckIn);
            }}
            showClearDate
            id={dateInputId}
            onDateChange={this.onChangeReleaseDate}
            value={expireAt}
          />
        </Grid.Column>
        <Grid.Column
          width={columnWidths.priceInput}
          textAlign={size === 'small' ? 'right' : undefined}
        >
          <FormField
            fluid={size !== 'small'}
            id={officialRateInputId}
            label={
              shouldShowLabel ? (
                <label htmlFor={officialRateInputId} style={{ marginRight: '5px', color: 'black' }}>
                  Official Rate
                </label>
              ) : (
                undefined
              )
            }
            type="number"
            disabled={!overwriteOfficialRate}
            name="officialRate"
            onChange={this.handleChange}
            control={AutohighlightInput}
            className={`qb-input qb-input-colored qb-input-colored-disabled ${
              discountRate ? 'is-disabled' : ''
            }`}
            icon={currencyIcon}
            iconPosition="left"
            value={parseFloat(this.getFloatValue(officialRate))}
          />
        </Grid.Column>
        <Grid.Column
          width={columnWidths.priceInput}
          textAlign={size === 'small' ? 'right' : undefined}
        >
          <FormField
            inline={size === 'small'}
            fluid={size !== 'small'}
            label={
              shouldShowLabel ? (
                <label htmlFor={discountInputId} style={{ marginRight: '5px' }}>
                  Discount
                </label>
              ) : (
                undefined
              )
            }
            control={AutohighlightInput}
            type="number"
            step="1"
            className="qb-input qb-input-colored qb-input-colored-red"
            id={discountInputId}
            icon="percent"
            name="discountRate"
            iconPosition="left"
            value={parseFloat(calculatePercentageFromValues(discountRate, officialRate))}
            onChange={(e, { value, name }) =>
              this.handleChange(e, { value: calculatePercentageValue(value, officialRate), name })
            }
          />
        </Grid.Column>
        <Grid.Column
          width={columnWidths.priceInput}
          textAlign={size === 'small' ? 'right' : undefined}
        >
          <div className="qb-input-average-rate-container">
            <FormField
              inline={size === 'small'}
              fluid={size !== 'small'}
              id={priceInputId}
              label={
                shouldShowLabel ? (
                  <label htmlFor={priceInputId} style={{ marginRight: '5px' }}>
                    Avg Rate
                  </label>
                ) : (
                  undefined
                )
              }
              control={AutohighlightInput}
              step="10"
              type="number"
              className="qb-input qb-input-colored qb-input-colored-orange"
              icon={currencyIcon}
              iconPosition="left"
              name="roomRate"
              value={parseFloat(this.getFloatValue(officialRate - discountRate))}
              onChange={this.handleChange}
            />
          </div>
        </Grid.Column>
        <Grid.Column
          width={columnWidths.priceInput}
          textAlign={size === 'small' ? 'right' : undefined}
        >
          <FormField
            inline={size === 'small'}
            fluid={size !== 'small'}
            control={AutohighlightInput}
            type="number"
            step="10"
            label={
              shouldShowLabel ? (
                <label htmlFor={finalPriceInputId} style={{ marginRight: '5px' }}>
                  Total
                </label>
              ) : (
                undefined
              )
            }
            className="qb-input qb-input-colored qb-input-colored-blue"
            id={finalPriceInputId}
            icon={currencyIcon}
            iconPosition="left"
            name="totalRoomRate"
            value={parseFloat(this.getFloatValue((officialRate - discountRate) * nights))}
            onChange={this.handleChange}
          />
        </Grid.Column>
      </Grid.Row>
    );
  }
}
