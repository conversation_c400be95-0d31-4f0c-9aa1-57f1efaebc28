import React, { Component } from 'react';

import { compose } from 'react-apollo';

import { <PERSON><PERSON>, <PERSON>ader, Message } from 'semantic-ui-react';
import moment from 'moment';
import PropTypes from 'prop-types';

import { withRouter } from 'react-router';

import uuid from 'utils/uuid';

import AvailabilityBreakdownTable from './AvailabilityBreakdownTable';
import AvailabilityTable from './AvailabilityTable';
import AvailabilityGroupTable from './AvailabilityGroupTable';
import {
  withAvailability,
  withGroupAvailability,
  withAvailabilityBreakdown
} from './AvailabilityQuery';
import withCreateProposal from './withCreateProposal';

import transformFiltersToMessage from './utils/transformFiltersToMessage';
import EmptyState from './emptyState';

import mapRatesToEvenets from './utils/mapRatesToEvents';
import { VIEW_TYPE } from './constants';
import withUserData from '../../hoc/withUserData';

export class AvailabilityViewTable extends Component {
  static propTypes = {
    rates: PropTypes.arrayOf(PropTypes.shape),
    loading: PropTypes.bool,
    error: PropTypes.string,
    actionButtons: PropTypes.arrayOf(
      PropTypes.shape({
        text: PropTypes.string,
        icon: PropTypes.string,
        color: PropTypes.string
      })
    ),
    variables: {
      nights: PropTypes.number,
      children: PropTypes.number,
      adults: PropTypes.number,
      infants: PropTypes.number,
      propertyCode: PropTypes.string,
      currency: PropTypes.string,
      rooms: PropTypes.number,
      checkIn: PropTypes.string,
      country: PropTypes.string
    },
    preferences: {
      discountWarning: PropTypes.number
    },
    createProposal: PropTypes.func,
    operatorId: PropTypes.string,
    history: PropTypes.shape({
      push: PropTypes.func
    })
  };

  static defaultProps = {
    actionButtons: [
      {
        color: 'blue',
        content: 'Create Proposal',
        icon: 'send'
      },
      {
        color: 'yellow',
        content: 'Add to waiting list',
        icon: 'calendar plus'
      }
    ]
  };

  state = { selectedRates: [] };

  createProposal = () => {
    const { variables, preferences: { discountWarning } } = this.props;

    const confirmed = this.state.selectedRates.every(rate => {
      if (rate.discountRate === 0) return true;

      const discountRateInRate = rate.discountRate / (rate.officialRate / 100);

      if (discountRateInRate > discountWarning) {
        return confirm(
          `Please confirm discount ${discountRateInRate}% of ${rate.room} - ${rate.rate}`
        );
      }

      return true;
    });

    if (!confirmed) {
      return;
    }

    const { view } = variables;

    const optionIds = view === VIEW_TYPE.GROUPED_ROOMS ? [uuid()] : undefined;

    const offers =
      view === VIEW_TYPE.BREAKDOWN
        ? this.state.selectedRates
        : this.state.selectedRates.map(rate => ({
            optionIds,
            title: rate.room,
            description: rate.rate,
            rateId: rate.rateId,
            nights: rate.nights || this.props.variables.nights,
            children: rate.children || this.props.variables.children,
            adults: rate.adults || this.props.variables.adults,
            infants: rate.infants || this.props.variables.infants,
            propertyCode: rate.propertyCode || this.props.variables.propertyCode,
            currency: rate.currency || this.props.variables.currency,
            rooms: rate.rooms || this.props.variables.rooms,
            rate: {
              id: rate.rateId,
              room: rate.accommodationCode,
              name: rate.rate,
              roomName: rate.room,
              currency: rate.currency || this.props.variables.currency
            },
            accommodation: [{ name: rate.room, code: rate.accommodationCode }],
            checkIn: rate.checkIn || this.props.variables.checkIn,
            releaseAt: rate.expireAt,
            officialRate: rate.officialRate,
            roomRate: rate.roomRate,
            discountRate: rate.discountRate,
            excludedCharges: rate.excludedCharges,
            cancellationExpiration: rate.cancellationExpiration,
            taxesRate: rate.taxesRate
          }));

    this.props.createProposal({
      operator: {
        id: this.props.operatorId,
        photo: {}
      },
      request: Object.assign({}, this.props.variables, {
        location: this.props.variables.country
      }),
      contact: {
        country: this.props.variables.country
      },
      offers
    });
    this.props.history.push('/proposals/new');
  };

  editSelectedRates = selectedRates => this.setState({ selectedRates });

  render() {
    const { selectedRates } = this.state;

    const {
      variables = {},
      rates,
      actionButtons,
      loading,
      error,
      preferences: { overwriteOfficialRate }
    } = this.props;
    const { view } = variables;

    if (loading) {
      return <Loader active />;
    }

    if (error) {
      return <Message error header="There was an unexpected error with your search." />;
    }

    if (!rates || (rates && rates.length === 0)) {
      return (
        <div
          style={{
            background: 'none',
            border: 'none',
            boxShadow: 'none',
            width: '100%',
            height: '100%',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            flexDirection: 'column'
          }}
        >
          <EmptyState />
          <Message
            style={{ boxShadow: 'none' }}
            header={rates ? 'No rates found for the search criteria' : 'Search for availability'}
          />
        </div>
      );
    }

    return (
      <div style={{ width: '100%' }}>
        {actionButtons &&
          actionButtons.length && (
            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
                width: '100%',
                position: 'sticky',
                top: '0',
                padding: '0.5em',
                zIndex: 10
              }}
            >
              {actionButtons.map((props, index) => (
                <Button
                  disabled={selectedRates && selectedRates.length === 0}
                  key={index}
                  onClick={this.createProposal}
                  {...props}
                />
              ))}
            </div>
          )}
        <Message
          content={transformFiltersToMessage(variables, rates.length)}
          style={{
            boxShadow: 'rgba(25, 17, 34, 0.05) 0 3px 10px',
            background: '#fff',
            color: '#6a6a6a'
          }}
        />
        {view === VIEW_TYPE.MULTIPLE_ROOMS && (
          <AvailabilityTable
            overwriteOfficialRate={overwriteOfficialRate}
            rates={rates}
            onSelectedRatesChange={this.editSelectedRates}
          />
        )}
        {view === VIEW_TYPE.GROUPED_ROOMS && (
          <AvailabilityGroupTable rates={rates} onSelectedRatesChange={this.editSelectedRates} />
        )}
        {view === VIEW_TYPE.BREAKDOWN && (
          <AvailabilityBreakdownTable
            dateRange={{
              from: variables.checkIn,
              to: moment(variables.checkIn)
                .add(variables.nights, 'days')
                .format()
            }}
            filters={variables}
            events={mapRatesToEvenets(rates)}
            onEditSelectedRates={this.editSelectedRates}
          />
        )}
      </div>
    );
  }
}

export default compose(
  withRouter,
  withCreateProposal,
  withAvailability,
  withGroupAvailability,
  withAvailabilityBreakdown,
  withUserData
)(AvailabilityViewTable);
