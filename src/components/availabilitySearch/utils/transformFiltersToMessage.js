import { getNounFormByNumber } from 'utils/stringUtils';
import { VIEW_TYPE } from '../constants';

const transformFiltersToMessage = (filters, ratesLength) => {
  const guests = filters.adults + filters.children + filters.infants;

  const nightsText = getNounFormByNumber(filters.nights, {
    singular: 'night',
    plural: 'nights'
  });

  const guestsText = getNounFormByNumber(guests, {
    singular: 'guest',
    plural: 'guests'
  });

  const guestsPhrase = filters.view === VIEW_TYPE.GROUPED_ROOMS ? '' : `for ${guestsText} `;

  return `Found ${ratesLength} available rates ${guestsPhrase}at ${
    filters.checkIn
  } for ${nightsText}`;
};

export default transformFiltersToMessage;
