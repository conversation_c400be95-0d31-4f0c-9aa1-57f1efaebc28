import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { Modal, Grid } from 'semantic-ui-react';

import AvailabilityFilters from './AvailabilityFilters';
import AvailabilityViewTable from './AvailabilityViewTable';
import { VIEW_TYPE } from './constants';

const Container = ({ actionButtons, modalTrigger, children }) =>
  modalTrigger ? (
    <Modal
      content={children}
      trigger={modalTrigger}
      actions={[{ content: 'Cancel', icon: 'cancel' }, ...actionButtons]}
    />
  ) : (
    <div>{children}</div>
  );

export class AvailabilitySearch extends Component {
  static propTypes = {
    portalTrigger: PropTypes.element,
    initialFilters: PropTypes.shape({
      view: PropTypes.oneOf([
        VIEW_TYPE.MULTIPLE_ROOMS,
        VIEW_TYPE.GROUPED_ROOMS,
        VIEW_TYPE.BREAKDOWN
      ]),
      propertyCode: PropTypes.string,
      currency: PropTypes.string,
      checkIn: PropTypes.string,
      nights: PropTypes.number,
      country: PropTypes.string,
      rooms: PropTypes.number,
      adults: PropTypes.number,
      children: PropTypes.number,
      infants: PropTypes.number
    })
  };

  static defaultProps = {
    initialFilters: {
      view: VIEW_TYPE.MULTIPLE_ROOMS,
      propertyCode: '',
      currency: '',
      checkIn: '',
      nights: 1,
      country: '',
      rooms: 1,
      adults: 1,
      children: 0,
      infants: 0
    }
  };

  constructor(props) {
    super(props);

    this.state = {
      filters: props.initialFilters,
      selectedRates: []
    };
  }

  handleEditSelectedRates = selectedRates => this.setState({ selectedRates });

  handleFiltersChange = filters => {
    this.setState({ filters, selectedRates: [] });
  };

  render() {
    const { filters } = this.state;

    return (
      <Container {...this.props}>
        <div
          style={{
            display: 'flex',
            width: '100%',
            height: '100vh'
          }}
        >
          <div>
            <AvailabilityFilters filters={filters} onSearch={this.handleFiltersChange} />
          </div>
          <div style={{ height: '100%', width: '100%', overflowY: 'auto' }}>
            <Grid divided="vertically" container style={{ height: '100%' }}>
              <AvailabilityViewTable
                variables={filters}
                selectedRates={this.state.selectedRates}
                onEditSelectedRates={this.handleEditSelectedRates}
              />
            </Grid>
          </div>
        </div>
      </Container>
    );
  }
}

export default AvailabilitySearch;
