import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { FormDropdown } from 'semantic-ui-react';
import { find } from 'lodash';

import { graphql } from 'react-apollo';
import getProperties from 'queries/getProperties.gql';

/**
 * Connects with the Apollo-Client and gets the properties
 * given the language variable.
 *
 * @class PropertiesDropdown
 */
class PropertiesDropdown extends Component {
  static propTypes = {
    language: PropTypes.string,
    value: PropTypes.string,
    propertyName: PropTypes.string,
    selection: PropTypes.bool,
    data: PropTypes.shape({
      loading: PropTypes.bool,
      properties: PropTypes.arrayOf(
        PropTypes.shape({
          name: PropTypes.string,
          value: PropTypes.string,
        })
      ),
    }),
    onChange: PropTypes.func,
  };

  static defaultProps = {
    language: 'en',
    selection: true,
    data: {
      loading: true,
      properties: [],
    },
  };

  componentWillReceiveProps({ data = {}, value = '' }) {
    if (data.properties && data.properties.length && !value) {
      this.props.onChange({
        value: data.properties.map(prop => prop.code)[0],
        name: 'propertyCode',
        from: data.properties[0].operation.open_from || null,
        to: data.properties[0].operation.open_to || null,
      });
    }

    if (this.props.withCurrency && data.properties && data.properties.length && !value) {
      this.props.onChange({
        value: data.properties.map(prop => prop.currency)[0],
        name: 'currency',
      });
    }
  }

  updateCurrency = () => {
    const { data = {}, value = '', withCurrency } = this.props;

    if (withCurrency && data.properties && data.properties.length) {
      const filteredProperties = data.properties.filter(prop => prop.code === value);
      const property = filteredProperties[0] || { currency: 'EUR' };
      const { currency } = property;
      this.props.onChange({
        value: currency,
        name: 'currency',
      });
    }
  };

  handleChange = (e, { value }) => {
    const { data } = this.props;
    let from = null;
    let to = null;
    const property = data.properties.filter(prop => prop.code === value).pop();

    if (property.operation && property.operation.open_from && property.operation.open_to) {
      from = property.operation.open_from;
      to = property.operation.open_to;
    }

    this.props.onChange({
      from,
      to,
      value,
      name: 'propertyCode'
    });

    this.updateCurrency();
  };

  render() {
    const { data: { loading, properties }, selection, propertyName, value } = this.props;

    const options = properties
      ? properties.map(property => ({
          value: property.code,
          text: property.name,
        }))
      : [];

    const isCurrentPropertyInOptions =
      propertyName && value ? Boolean(find(options, { value })) : true;

    if (!isCurrentPropertyInOptions) {
      options.push({
        value,
        text: propertyName,
      });
    }

    return (
      <FormDropdown
        label="Property"
        placeholder="Select a property"
        selection={selection}
        loading={loading}
        options={options}
        name="propertyCode"
        value={value}
        selectOnBlur={false}
        onChange={this.handleChange}
      />
    );
  }
}

export default graphql(getProperties, {
  skip: ownProps => !ownProps.language,
})(PropertiesDropdown);
