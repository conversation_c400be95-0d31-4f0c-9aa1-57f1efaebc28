.ui.vertical.menu.qb-sidebar-menu {
  min-height: 100vh;
  border-radius: 0;
  border: 0;
  overflow-y: auto;
  padding-bottom: 80px;

  .item.qb-sidebar-menu-item {
    &.active {
      background: #dbe6fe;
    }
  }

  .qb-date-range-picker {
    .DateInput {
      font-size: 14px !important;
    }
  }

  // TODO: FIND A BETTER NAME
  .qb-sidebar-menu-cta-button-container-no-absolute {
    display: flex;
    justify-content: center;
    width: 100%;

    .ui.button {
      background-color: #4680ff;
      box-shadow: 1px 1px 40px 0 rgba(0, 0, 0, 0.2);

      &:active {
        background-color: #568bff;
      }
    }
  }
}
