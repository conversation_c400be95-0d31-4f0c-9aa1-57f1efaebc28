import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { <PERSON><PERSON>, <PERSON>u, Icon, Form } from 'semantic-ui-react';
import { DateRangePicker } from 'components/datePicker';
import { NumberInput, LabelPerRoom } from 'components';

import PropertiesDropdown from './propertiesDropdown';
import LocationDropdown from './locationDropdown';

import { VIEW_TYPE } from '../constants';

import './AvailabilityFilters.scss';

const validateFilters = filters => {
  if (filters.view === VIEW_TYPE.MULTIPLE_ROOMS || VIEW_TYPE.BREAKDOWN) {
    return (
      filters.propertyCode &&
      filters.checkIn &&
      filters.nights >= 1 &&
      filters.rooms >= 1 &&
      filters.adults
    );
  } else if (filters.view === VIEW_TYPE.GROUPED_ROOMS) {
    return filters.propertyCode && filters.checkIn && filters.nights >= 1;
  }

  return true;
};

class AvailabilityFilters extends Component {
  static propTypes = {
    onSearch: PropTypes.func,
    filters: PropTypes.shape({
      view: PropTypes.oneOf([
        VIEW_TYPE.MULTIPLE_ROOMS,
        VIEW_TYPE.GROUPED_ROOMS,
        VIEW_TYPE.BREAKDOWN
      ]),
      propertyCode: PropTypes.string,
      rooms: PropTypes.number,
      adults: PropTypes.number,
      children: PropTypes.number,
      infants: PropTypes.number,
      checkIn: PropTypes.string,
      nights: PropTypes.number,
      country: PropTypes.string,
      openFrom: PropTypes.string,
      openTo: PropTypes.string
    })
  };

  constructor(props) {
    super(props);

    this.state = {
      filters: props.filters
    };
  }

  toggleViewToBreakdown = (e, { checked }) => {
    const newViewType = checked ? VIEW_TYPE.BREAKDOWN : VIEW_TYPE.MULTIPLE_ROOMS;
    this.updateFilters({
      view: newViewType
    });
  };

  toggleViewToGroupedRooms = (e, { checked }) => {
    const newViewType = checked ? VIEW_TYPE.GROUPED_ROOMS : VIEW_TYPE.MULTIPLE_ROOMS;
    this.updateFilters({
      view: newViewType,
      rooms: 1,
      adults: 1,
      children: 0,
      infants: 0
    });
  };

  updateFilters = newFilters => {
    this.setState(({ filters }) => ({
      filters: {
        ...filters,
        ...newFilters
      }
    }));
  };

  handleDatesChange = ({ checkIn, nights }) => {
    this.updateFilters({ checkIn, nights });
  };

  handleFieldChange = ({ name, value }) => {
    this.updateFilters({ [name]: value });
  };

  handleFieldWithEventChange = (e, { name, value }) => {
    this.updateFilters({ [name]: value });
  };

  handleSearch = () => {
    this.props.onSearch(this.state.filters);
  };

  handlePropertyCodeChange = ({ name, value, from, to }) => {
    const update = { [name]: value };
    if (from !== undefined && to !== undefined) {
      // handle from definition
      update.openFrom = from;
      update.openTo = to;
    }
    this.updateFilters(update);
  };

  render() {
    const { filters } = this.state;

    const {
      rooms,
      adults,
      children,
      infants,
      propertyCode,
      openFrom,
      openTo,
      checkIn,
      country,
      nights,
      view
    } = filters;

    const areFiltersValid = validateFilters(filters);

    return (
      <Menu className="qb-sidebar-menu" {...this.props} vertical size="large" borderless>
        <Menu.Item>
          <Menu.Header style={{ margin: 0 }}>
            <Icon
              color="blue"
              name="filter"
              circular
              inverted
              style={{
                marginRight: '15px',
                background: '#4680FF',
                boxShadow: '0 2px 10px 0 rgba(70,128,255,0.70)'
              }}
            />
            Search Filters
          </Menu.Header>
        </Menu.Item>
        <Menu.Item>
          <Form>
            <PropertiesDropdown
              withCurrency
              onChange={this.handlePropertyCodeChange}
              value={propertyCode}
            />
            <Form.Checkbox
              id="groupOffers"
              name="groupOffers"
              label="Group Offers"
              checked={view === VIEW_TYPE.GROUPED_ROOMS}
              onChange={this.toggleViewToGroupedRooms}
            />
            {view !== VIEW_TYPE.GROUPED_ROOMS && (
              <Form.Group widths={16} grouped>
                <NumberInput
                  label="Rooms"
                  onChange={this.handleFieldChange}
                  value={rooms}
                  name="rooms"
                  icon="hotel"
                  min={1}
                />
                <NumberInput
                  label={<LabelPerRoom label="Adults" rooms={rooms} horizontal={false} />}
                  name="adults"
                  onChange={this.handleFieldChange}
                  value={adults}
                  icon="user"
                  min={1}
                />
                <NumberInput
                  label={<LabelPerRoom label="Children" rooms={rooms} horizontal={false} />}
                  name="children"
                  onChange={this.handleFieldChange}
                  value={children}
                  icon="child"
                  min={0}
                />
                <NumberInput
                  label={<LabelPerRoom label="Infants" rooms={rooms} horizontal={false} />}
                  name="infants"
                  onChange={this.handleFieldChange}
                  value={infants}
                  icon="child"
                  min={0}
                />
              </Form.Group>
            )}
            <Form.Field
              label="Dates"
              control={DateRangePicker}
              numberOfMonths={2}
              fluid
              startDateId="date"
              onDatesChange={this.handleDatesChange}
              checkIn={checkIn}
              from={openFrom}
              to={openTo}
              nights={nights}
            />
            <Form.Field
              control={LocationDropdown}
              value={country}
              id="country"
              name="country"
              label="Market"
              search
              selection
              onChange={this.handleFieldWithEventChange}
            />
            <Form.Checkbox
              id="breakdown"
              name="isBreakdown"
              label="Daily breakdown"
              checked={view === VIEW_TYPE.BREAKDOWN}
              onChange={this.toggleViewToBreakdown}
            />
          </Form>
        </Menu.Item>
        <div className="qb-sidebar-menu-cta-button-container-no-absolute">
          <Button
            disabled={!areFiltersValid}
            icon="search"
            content="Search"
            color="teal"
            onClick={this.handleSearch}
          />
        </div>
      </Menu>
    );
  }
}

export default AvailabilityFilters;
