import { graphql, compose } from 'react-apollo';

import getCountryCodes from 'queries/getCountryCodes.gql';
import LocationDropdown from './locationDropdown';

const LocationDropdownDataContainer = graphql(getCountryCodes, {
  props: ({ data }) => ({
    loading: data.loading,
    options: data.countryCodes
      ? data.countryCodes.map(country => ({
          text: country.countryName,
          value: country.code,
        }))
      : [],
  }),
});

export default compose(LocationDropdownDataContainer)(LocationDropdown);
