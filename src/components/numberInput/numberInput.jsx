import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { FormField, Button, Icon, Label } from 'semantic-ui-react';

class NumberInput extends Component {
  static propTypes = {
    max: PropTypes.number,
    min: PropTypes.number,
    value: PropTypes.number,
    onChange: PropTypes.func,
    label: PropTypes.string,
    icon: PropTypes.string,
    name: PropTypes.string,
    error: PropTypes.bool,
  };

  constructor(props) {
    super(props);

    this.state = {
      touched: false,
    };
  }

  onIncrement = e => {
    e.preventDefault();

    const { value, onChange, max, name } = this.props;

    if ((!max && max !== 0) || max >= value + 1) {
      onChange({ value: value + 1, name });
    }
  };

  onDecrement = e => {
    e.preventDefault();

    const { value, onChange, min, name } = this.props;

    if ((!min && min !== 0) || min <= value - 1) {
      onChange({ value: value - 1, name });
    }
  };

  onBlur = () => {
    this.setState({ touched: true });
  };

  render() {
    const { label, icon, value, min, max, error } = this.props;
    const { touched } = this.state;
    return (
      <FormField>
        <div
          onBlur={this.onBlur}
          style={{
            width: '100%',
            maxWidth: '300px',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Icon name={icon} />
            <div>{label}</div>
          </div>
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
            }}
          >
            <Button
              circular
              size="small"
              basic
              icon="minus"
              onClick={this.onDecrement}
              disabled={min >= value}
            />
            <div
              style={{
                textAlign: 'center',
                fontSize: '1.2em',
                width: '30px',
              }}
            >
              {value || 0}
            </div>
            <Button
              circular
              basic
              size="small"
              icon="plus"
              onClick={this.onIncrement}
              disabled={max <= value}
            />
          </div>
        </div>
        {error &&
          touched && (
            <Label basic color="yellow" pointing>
              Please enter a value
            </Label>
          )}
      </FormField>
    );
  }
}

export default NumberInput;
