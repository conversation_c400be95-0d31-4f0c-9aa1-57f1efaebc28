import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Item, Header, Loader, Icon } from 'semantic-ui-react';

import './list.scss';

class List extends Component {
  render() {
    const { items, width, loading, label, activeId, placeholder } = this.props;

    return (
      <div className="qb-list">
        <div>
          <Header>
            <Loader
              className="qb-list-loader"
              active={loading}
              inline
              style={{
                float: 'right',
                color: 'orange',
              }}
            />
          </Header>
          {items && items.length && items.length > 0 ? (
            <div>
              <Item.Group>{items}</Item.Group>
            </div>
          ) : (
            <div>
              {items &&
                !items.length &&
                !loading && (
                  <div
                    style={{
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      flexDirection: 'column',
                      width: '100%',
                      height: '100%',
                    }}
                  >
                    {placeholder}
                    <Header style={{ color: '#4A4A4A' }}>
                      No results found with these criteria. Try another search.
                    </Header>
                  </div>
                )}
            </div>
          )}
        </div>
      </div>
    );
  }
}

List.propTypes = {
  loading: PropTypes.bool.isRequired,
  activeId: PropTypes.string,
  label: PropTypes.string,
  width: PropTypes.number,
  placeholder: PropTypes.instanceOf(Object),
  // items: PropTypes.arrayOf(PropTypes.instanceOf(ListItem)).isRequired
};

export default List;
