/* global __CONFIG__ */
import React, { Component } from 'react';
import Intercom /* { IntercomAPI } */ from 'react-intercom';
import PropTypes from 'prop-types';

// @TODO Use IntercomAPI to track events. Probable inside a dedicated Intercom middleware that checks a list of
// actions and sends them to the tracking API.
export default class IntercomChat extends Component {
  static propTypes = {
    email: PropTypes.string,
    intercomHash: PropTypes.string,
    fullName: PropTypes.string,
    proposal_sent: PropTypes.string,
    accountName: PropTypes.string,
    operatorId: PropTypes.string,
    isAdmin: PropTypes.string,
  };

  render() {
    const {
      email,
      intercomHash,
      fullName,
      proposal_sent,
      accountName,
      operatorId,
      isAdmin,
    } = this.props;

    const { version } = __CONFIG__;

    const options = {
      user_id: operatorId,
      user_hash: intercomHash,
      email,
      company: {
        id: accountName,
        name: accountName,
      },
      name: fullName,
      latest_proposal_sent_at: proposal_sent || null,
      version,
    };

    return isAdmin ? null : (
      <div className="intercom-ui">
        <Intercom
          appID={__CONFIG__.intercom.appId}
          alignment="left"
          horizontal_padding="20"
          show_powered_by={false}
          {...options}
          custom_launcher_selector="#intercom-widget"
        />
      </div>
    );
  }
}
