import { connect } from 'react-redux';
import { graphql, compose } from 'react-apollo';
import getUser from 'queries/getUser.gql';
import getOperator from 'queries/getOperator.gql';
import { get } from 'lodash';
import { getEventByName } from 'state/selectors/events';

import Intercom from './intercom';
import getIsUserAdmin from 'state/selectors/user/getIsUserAdmin';

const IntercomDataContainer = graphql(getUser, {
  props: ({ data }) => ({
    operatorId: get(data, 'userInfo.operatorId'),
    intercomHash: get(data, 'userInfo.intercomHash'),
    accountName: get(data, 'userInfo.accountName'),
    loading: data.loading,
    language: 'en',
  }),
});

const IntercomStateContainer = connect(state => ({
  proposal_sent: getEventByName(state, { name: 'proposal_sent' }),
  isAdmin: getIsUserAdmin(state),
}));

const IntercomOperatorDetailsContainer = graphql(getOperator, {
  props: ({ data, ownProps }) => ({
    fullName: get(data, 'operator.fullName'),
    email: get(data, 'operator.email'),
    loading: ownProps.loading && data.loading,
  }),
  skip: ownProps => !ownProps.operatorId || !ownProps.language,
});

export default compose(
  IntercomStateContainer,
  IntercomDataContainer,
  IntercomOperatorDetailsContainer
)(Intercom);
