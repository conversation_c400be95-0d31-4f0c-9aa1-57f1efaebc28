const getPreviewLoadingTemplate = () => `
<!DOCTYPE html>
<html>
  <body>
    <style>
      .qb-preview-loader {
        width: 100%;
        height: 100%;
        height: 100vh;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }

      .qb-preview-loader-img {
        width: 90%;
        max-width: 500px;
      }

      .qb-preview-anim {
        animation: Anim 3.5s infinite linear;
      }

      .qb-preview-loader-text {
        text-align: center;
        font-size: 18px;
        color: #7b7b7b;
      }

      @keyframes Anim {
        0% {
          opacity: 0.3;
        }
        50% {
          opacity: 1;
        }
        100% {
          opacity: 0.3;
        }
      }
    </style>
    <div class="qb-preview-loader">
      <div class="qb-preview-loader-img">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 129 129" width="100%" xmlns:xlink="http://www.w3.org/1999/xlink">
              <defs>
                <circle id="a" cx="64.5" cy="64.5" r="64.5"/>
              </defs>
              <g fill="none" fill-rule="evenodd">
                <g>
                  <g>
                    <g>
                      <mask id="b" fill="#fff">
                        <use xlink:href="#a"/>
                      </mask>
                      <use fill="#F8B451" xlink:href="#a"/>
                      <g class="qb-preview-anim">
                      <rect width="83" height="102" x="23" y="27" fill="#F9F9F9" mask="url(#b)" rx="3"/>
                      <rect width="14" height="14" x="28" y="35" fill="#EFEFEF" mask="url(#b)" rx="2"/>
                      <rect width="57" height="3" x="44" y="35" fill="#FFF" mask="url(#b)" rx="1"/>
                      <rect width="23" height="3" x="44" y="41" fill="#FFF" mask="url(#b)" rx="1"/>
                      <rect width="14" height="14" x="28" y="55" fill="#FFF" mask="url(#b)" rx="2"/>
                      <rect width="57" height="3" x="44" y="55" fill="#FFF" mask="url(#b)" rx="1"/>
                      <rect width="23" height="3" x="44" y="61" fill="#FFF" mask="url(#b)" rx="1"/>
                      <rect width="14" height="14" x="28" y="75" fill="#FFF" mask="url(#b)" rx="2"/>
                      <rect width="57" height="3" x="44" y="75" fill="#FFF" mask="url(#b)" rx="1"/>
                      <rect width="23" height="3" x="44" y="81" fill="#FFF" mask="url(#b)" rx="1"/>
                      <rect width="14" height="14" x="28" y="95" fill="#FFF" mask="url(#b)" rx="2"/>
                      <rect width="57" height="3" x="44" y="95" fill="#FFF" mask="url(#b)" rx="1"/>
                      </g>
                    </g>
                  </g>
                </g>
              </g>
            </svg>
      </div>
      <div class="qb-preview-loader-text">
        Loading the preview...
      </div>
    </div>
  </body>
</html>
`;

export default getPreviewLoadingTemplate;
