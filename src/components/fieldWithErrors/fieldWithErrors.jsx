import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { Label } from 'semantic-ui-react';

import './fieldWithErrors.scss';

export default function fieldWithErrors(WrappedComponent) {
  return class FieldWithErrors extends Component {
    static propTypes = {
      hasError: PropTypes.bool,
      errorMessage: PropTypes.string,
    };

    render() {
      const { hasError, errorMessage, ...props } = this.props;

      return (
        <div className="qb-field-with-errors" onBlur={this.handleBlur}>
          <WrappedComponent {...props} />
          {hasError && (
            <div>
              <Label
                pointing
                basic
                color="yellow"
                content={errorMessage || 'Please fill in a value'}
              />
            </div>
          )}
        </div>
      );
    }
  };
}
