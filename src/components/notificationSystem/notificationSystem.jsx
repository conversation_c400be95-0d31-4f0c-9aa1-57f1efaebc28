import React, { Component } from 'react';
import PropTypes from 'prop-types';

import Notification from './notification';

import './notificationSystem.scss';

class NotificationSystem extends Component {
  render() {
    const { notifications, removeNotification } = this.props;

    return (
      <div className="qb-notification-system">
        {notifications.map(({ header, content, type, id, actions, dismissAfter, onDismiss }) => (
          <Notification
            key={id}
            onDismiss={() => {
              if (onDismiss) {
                onDismiss(id);
              }
              removeNotification(id);
            }}
            header={header}
            content={content}
            status={type}
            actions={actions}
            dismissAfter={dismissAfter}
          />
        ))}
      </div>
    );
  }
}

NotificationSystem.propTypes = {
  notifications: PropTypes.array,
  removeNotification: PropTypes.func,
};

export default NotificationSystem;
