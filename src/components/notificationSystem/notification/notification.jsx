import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { Message, Button, Icon } from 'semantic-ui-react';

import './notification.scss';

class Notification extends Component {
  static propTypes = {
    onDismiss: PropTypes.func,
    icon: PropTypes.string,
    header: PropTypes.string,
    content: PropTypes.string,
    status: PropTypes.oneOf(['info', 'error', 'warning', 'success']),
    dismissAfter: PropTypes.number,
    actions: PropTypes.arrayOf(
      PropTypes.shape({
        label: PropTypes.string,
        callback: PropTypes.func,
      })
    ),
  };

  static defaultProps = {
    status: 'info',
    content: '',
    header: '',
    dismissAfter: 4000,
  };

  componentDidMount() {
    const { dismissAfter, onDismiss, status } = this.props;
    // We want the user to manually dismiss the error notifications
    if (status !== 'error' && dismissAfter) {
      setTimeout(onDismiss, dismissAfter);
    }
  }

  render() {
    const { icon, header, content, onDismiss, status, actions } = this.props;

    const statusProp = {
      [`${status}`]: true,
    };

    const actionButtons =
      actions && actions.length
        ? actions.map(({ label, callback, isPrimary }, index) => (
          <Button
              key={index}
              basic
              size="tiny"
              primary={isPrimary}
              content={label}
              onClick={callback}
            />
          ))
        : null;

    const messageContent = (
      <div
        className="qb-notification-content"
        style={{
          display: 'flex',
          flexDirection: 'column',
        }}
      >
        {content}
        <div className="qb-notification-actions">{actionButtons}</div>
      </div>
    );

    const defaultIcon = {
      info: 'info',
      error: 'warning circle',
      warning: 'warning',
      success: 'check circle',
    }[status];

    const finalIcon = icon || defaultIcon;

    return (
      <div className="qb-notification-wrapper">
        <Message
          className="qb-notification"
          visible
          onDismiss={onDismiss}
          onClick={onDismiss}
          {...statusProp}
        >
          <Message.Header>
            <Icon name={finalIcon} />
            {header}
          </Message.Header>
          <Message.Content>{messageContent}</Message.Content>
        </Message>
      </div>
    );
  }
}

export default Notification;
