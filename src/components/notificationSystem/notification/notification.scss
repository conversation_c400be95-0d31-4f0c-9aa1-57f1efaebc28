.qb-notification-wrapper {
  margin-bottom: 10px;
  background: transparent;
  animation: animateIn 0.4s ease;
}

.qb-notification {
  border: 1px solid rgba(0, 0, 0, 0.15);
  box-shadow: rgba(25, 17, 34, 0.05) 0 3px 10px !important;
  transition: all 0.4s cubic-bezier(0.075, 0.82, 0.165, 1) !important;
  cursor: pointer;

  &:hover,
  &:focus {
    transform: translateY(-4px);
    box-shadow: rgba(25, 17, 34, 0.1) 0 10px 42px !important;
  }
}

.qb-notification-actions {
  margin-top: 10px;
  text-align: right;
}

@keyframes animateIn {
  from {
    transform: translateX(400px);
  }

  to {
    transform: translateX(0);
  }
}
