import { connect } from 'react-redux';

import { getNotifications } from 'state/selectors/notifications';
import removeNotification from 'state/actions/notifications/removeNotification';

import NotificationSystem from './notificationSystem';

const NotificationSystemContainer = connect(
  state => ({
    notifications: getNotifications(state),
  }),
  dispatch => ({
    removeNotification: id => dispatch(removeNotification(id)),
  })
)(NotificationSystem);

export default NotificationSystemContainer;
