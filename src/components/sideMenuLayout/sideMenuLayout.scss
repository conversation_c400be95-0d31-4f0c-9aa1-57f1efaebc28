.qb-sidemenu-layout-container {
  width: auto;
  position: relative;
  height: 100%;
  display: flex;

  &.is-open {
    .qb-sidemenu-layout-menu {
      transition: transform 0.4s ease;
      transform: translateX(0);
    }

    .qb-sidemenu-layout-content {
      margin-left: 260px;
      transition: all 0.4s ease;
    }
  }
}

.qb-sidemenu-layout-menu {
  height: 100vh;
  width: 260px;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  transform: translateX(-100%);
  transition: transform 0.4s ease;

  .qb-sidebar-menu.ui.menu {
    width: 100%;
    height: 100%;
    border-radius: 0px;
    border: none;
  }

  .ui.vertical.menu .menu .item {
    font-size: 1em;
  }
}

.qb-sidemenu-layout-content {
  width: 100%;
  margin-left: 0;
  transition: all 0.4s ease;
  min-height: 100%;
  min-height: 100vh;
}
