import React from 'react';
import { bool } from 'prop-types';
import { Modal, Button } from 'semantic-ui-react';

const SessionExpiredModal = ({ isLoggedIn = true }) => (
  <Modal open={!isLoggedIn}>
    <Modal.Header>Your Session has Expired</Modal.Header>
    <Modal.Content>
      <p>You will be redirected in order to provide your credentials</p>
    </Modal.Content>
    <Modal.Actions>
      <a href="/users/sign_out">
        <Button
          color="blue"
          icon="arrow right"
          labelPosition="right"
          content={'Go to login page'}
        />
      </a>
    </Modal.Actions>
  </Modal>
);

SessionExpiredModal.propTypes = {
  isLoggedIn: bool,
};

export default SessionExpiredModal;
