import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { Menu, Icon } from 'semantic-ui-react';
import { isMobile } from 'utils/mediaQueries';
import { Link, withRouter } from 'react-router-dom';
import Logo from '../logo';
import './navbar.scss';

class Navbar extends Component {
  static propTypes = {
    activeItem: PropTypes.string,
    shouldShowMessages: PropTypes.bool,
    shouldShowWaitingList: PropTypes.bool,
    shouldShowUpgrades: PropTypes.bool,
    location: PropTypes.shape({
      pathname: PropTypes.string,
    }),
  };

  constructor(props) {
    super(props);

    this.state = {
      activeItem: props.activeItem,
    };
  }

  render() {
    const { shouldShowMessages, location, shouldShowWaitingList, shouldShowUpgrades } = this.props;
    const { pathname } = location;
    const activeItem = pathname ? pathname.split('/')[1] : '';
    const isMob = isMobile();

    return (
      <Menu
        className="qb-navbar-main"
        vertical={!isMob}
        fixed={isMob ? 'bottom' : 'left'}
        borderless
        icon="labeled"
      >
        <Menu.Item className="qb-navbar-nav-item" name="quotelier">
          <Logo />
        </Menu.Item>

        <Menu.Item
          as={Link}
          to="/"
          name="proposals"
          className="qb-navbar-nav-item"
          active={activeItem === '' || activeItem === 'proposals'}
        >
          <Icon size="large" name="tasks" color="blue" />
          <div className="qb-navbar-nav-item-text">Proposals</div>
        </Menu.Item>

        <Menu.Item
          as={Link}
          to="/availability"
          name="availability"
          className="qb-navbar-nav-item"
          active={activeItem === 'availability'}
          color="blue"
        >
          <Icon size="large" name="calendar" color="blue" />
          <div className="qb-navbar-nav-item-text">Availability</div>
        </Menu.Item>

        <Menu.Item
          as={Link}
          to="/search"
          name="search"
          className="qb-navbar-nav-item"
          active={activeItem === 'search'}
          color="blue"
        >
          <Icon size="large" name="search" color="blue" />
          <div className="qb-navbar-nav-item-text">Search</div>
        </Menu.Item>

        <Menu.Item
          as={Link}
          to="/upsales-proposals"
          name="upsales-proposals"
          className="qb-navbar-nav-item"
          active={activeItem === 'upsales-proposals' || activeItem === 'opportunities'}
          color="blue"
        >
          <Icon size="large" name="line chart" color="blue" />
          <div className="qb-navbar-nav-item-text">Upsales</div>
        </Menu.Item>

        {!!shouldShowWaitingList && (
          <Menu.Item
            as={Link}
            to="/waitingList"
            name="waitingList"
            className="qb-navbar-nav-item"
            active={activeItem === 'waitingList'}
            color="blue"
            style={{
              alignItems: 'center',
            }}
          >
            <Icon.Group size="big" color="blue" style={{ margin: '0 auto .5rem', width: '28px' }}>
              <Icon name="time" color="blue" />
              <Icon corner name="mail" color="blue" />
            </Icon.Group>
            <div className="qb-navbar-nav-item-text">Waitlist</div>
          </Menu.Item>
        )}

        {!!shouldShowMessages && (
          <Menu.Item
            as={Link}
            to="/cms"
            name="cms"
            className="qb-navbar-nav-item"
            active={activeItem === 'cms'}
            color="blue"
          >
            <Icon size="large" name="mail outline" color="blue" />
            <div className="qb-navbar-nav-item-text">Messages</div>
          </Menu.Item>
        )}

        {!!shouldShowUpgrades && (
          <Menu.Item
            as={Link}
            to="/upgrades"
            name="upgrades"
            className="qb-navbar-nav-item"
            active={activeItem === 'upgrades'}
            color="blue"
          >
            <Icon size="large" name="arrow circle outline up" color="blue" />
            <div className="qb-navbar-nav-item-text">Upgrades</div>
          </Menu.Item>
        )}

        <Menu.Item
          id="intercom-widget"
          style={{
            position: 'absolute',
            textAlign: 'center',
            bottom: '80px',
            padding: 0,
            left: 0,
          }}
          as="a"
          name="help"
          className="qb-navbar-nav-item"
          active={activeItem === 'help'}
        >
          <Icon size="large" name="comment" color="teal" />
          <div className="qb-navbar-nav-item-text">Ask me anything</div>
        </Menu.Item>
        <Menu.Item
          style={{
            position: 'absolute',
            textAlign: 'center',
            bottom: '0',
            padding: 0,
            left: 0,
          }}
          as="a"
          name="user"
          className="qb-navbar-nav-item"
          // active={activeItem === 'operator'}
          href="/users/sign_out"
        >
          <Icon size="large" name="power" color="grey" />
          <div className="qb-navbar-nav-item-text">Log out</div>
        </Menu.Item>
      </Menu>
    );
  }
}

export default withRouter(Navbar);
