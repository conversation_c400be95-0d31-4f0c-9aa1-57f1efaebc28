import { connect } from 'react-redux';
import { withRouter } from 'react-router';
import { getUserHasPermissionInAnyProperty } from 'state/selectors/user';

import Navbar from './navbar';

export default withRouter(
  connect(state => ({
    shouldShowMessages: getUserHasPermissionInAnyProperty(state, 'cms'),
    shouldShowWaitingList: getUserHasPermissionInAnyProperty(state, 'waiting-list'),
    shouldShowUpsales: getUserHasPermissionInAnyProperty(state, 'upgrades'),
  }))(Navbar)
);
