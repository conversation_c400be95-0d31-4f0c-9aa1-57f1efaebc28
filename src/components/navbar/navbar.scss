body {
  background: #F6F7FB;
}

.qb-navbar-nav-item-text {
  font-size: 12px;
}

.ui.vertical.menu.qb-navbar-main {
  width: 84px;

  .item.qb-navbar-nav-item {
    height: 80px;
    width: 80px;
    display: flex;
    justify-content: center;
    align-content: center;
    transition: background-color 0.4s ease;

    &::after {
      // transform-origin: top;
      content: '';
      transform: scale(0);
      transition: all 0.4s ease;
      position: absolute;
      top: 20%;
      left: 0;
      width: 5px;
      background: #4680ff;
      height: 60%;
      z-index: 200;
      border-radius: 4px;
      box-shadow: rgba(70, 128, 255, 0.3) 0 4px 10px;
    }

    &.active {
      background: none !important;

      &::after {
        transform: scale(1);
      }
    }

    &:hover {
      background: #dbe6fe;
    }
  }
}
