import { graphql, compose } from 'react-apollo';
import { connect } from 'react-redux';

import getOperator from 'queries/getOperator.gql';
import { getUserOperatorId } from 'state/selectors/user';
import { get } from 'lodash';

import OperatorImage from './operatorImage';

const OperatorImageDataContainer = graphql(getOperator, {
  props: ({ data }) => ({
    imageUrl: get(data, 'operator.photo.small') || get(data, 'operator.photo.medium'),
  }),
  skip: ownProps => !ownProps.language || !ownProps.operatorId,
});

const OperatorImageContainer = connect(state => ({
  operatorId: getUserOperatorId(state),
  language: 'en',
}));

export default compose(OperatorImageContainer, OperatorImageDataContainer)(OperatorImage);
