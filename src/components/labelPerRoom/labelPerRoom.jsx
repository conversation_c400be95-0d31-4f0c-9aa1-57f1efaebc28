import React from 'react';
import PropTypes from 'prop-types';

const LabelPerRoom = ({ label, rooms, horizontal = true }) => (
  <div style={{ display: 'inline-flex', flexDirection: horizontal ? 'row' : 'column' }}>
    {label}{' '}
    {rooms > 1 ? (
      <div style={{ color: '#a0a1a3', marginLeft: horizontal ? '5px' : '', fontSize: '12px' }}>
        (Per room)
      </div>
    ) : (
      ''
    )}
  </div>
);

LabelPerRoom.propTypes = {
  label: PropTypes.any,
  rooms: PropTypes.number,
  horizontal: PropTypes.bool,
};

export default LabelPerRoom;
