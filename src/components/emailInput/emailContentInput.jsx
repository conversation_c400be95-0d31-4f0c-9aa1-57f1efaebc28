import React from 'react';
import PropTypes from 'prop-types';
import { Icon } from 'semantic-ui-react';
import fieldWithErrors from '../fieldWithErrors';

const EmailContentInput = props => (
  <div className="ui left icon input">
    <Icon name="mail" />
    <input
      {...props}
      onChange={e => {
        props.onChange(e, { name: props.name, value: e.target.value });
      }}
    />
    {!props.isCarbonCopyEnabled && (
      <button
        onClick={props.onCarbonCopyClick}
        style={{
          position: 'absolute',
          right: 5,
          bottom: 9,
          color: 'black',
          cursor: 'pointer',
          background: 'white',
          border: 0,
        }}
      >
        Cc
      </button>
    )}
  </div>
);

EmailContentInput.propTypes = {
  name: PropTypes.string,
  isCarbonCopyEnabled: PropTypes.bool,
  onCarbonCopyClick: PropTypes.func,
  onChange: PropTypes.func,
};

export default fieldWithErrors(EmailContentInput);
