import React, { Component } from 'react';
import PropTypes from 'prop-types';
import { Form, Dropdown } from 'semantic-ui-react';
import { some, get } from 'lodash';
import { withApollo } from 'react-apollo';
import getEmailStatus from 'queries/getEmailStatus.gql';
import isValidEmail from 'utils/validation/isEmail';
import fieldWithErrors from '../fieldWithErrors';
import EmailContentInput from './emailContentInput';

const DropdownWithErrors = fieldWithErrors(Dropdown);

class EmailInput extends Component {
  static propTypes = {
    email: PropTypes.string,
    cc: PropTypes.string,
    errors: PropTypes.shape({
      mail: PropTypes.bool,
      cc: PropTypes.bool,
    }),
    client: PropTypes.shape({
      query: PropTypes.func,
    }),
    onChange: PropTypes.func,
  };

  constructor(props) {
    super(props);

    this.state = {
      isCarbonCopyEnabled: Boolean(props.cc && props.cc.length > 0),
      blacklist: [],
    };
  }

  componentWillReceiveProps(nextProps) {
    if (!this.state.withCC && nextProps.cc && nextProps.cc.length > 0) {
      this.setState({ withCC: true });
    }
  }

  onFieldChange = (e, { value, name }) => {
    e.preventDefault();
    this.props.onChange(name, value);
  };

  getIsEmailBlacklisted = email => {
    const { client } = this.props;
    const { blacklist } = this.state;

    // We only check for blacklisted emails that are not in our list
    // and are valid emails
    if (!email || !isValidEmail(email) || blacklist.indexOf(email) !== -1) {
      return;
    }

    client
      .query({
        query: getEmailStatus,
        variables: {
          email,
        },
      })
      .then(({ data }) => {
        const isEmailBlacklisted = get(data, 'emailStatus.isBlacklisted');

        if (isEmailBlacklisted) {
          this.setState(({ blacklist: prevBlacklist }) => ({
            blacklist: [...prevBlacklist, email],
          }));
        }
      })
      .catch(console.log); // eslint-disable-line
  };

  enableCarbonCopyInput = () => {
    this.setState({
      isCarbonCopyEnabled: true,
    });
  };

  handleBlur = e => {
    const email = get(e, 'target.value', '');

    this.getIsEmailBlacklisted(email);
  };

  handleAdd = (e, { value }) => {
    this.getIsEmailBlacklisted(value);
  };

  isAnyCcBlacklisted = (cc, blacklist) => some(cc, email => blacklist.indexOf(email) !== -1);

  isEmailBlacklisted = (blacklist, email) => blacklist.indexOf(email) !== -1;

  renderEmailLabel = (label, index) => {
    const { blacklist } = this.state;
    const hasError = this.props.errors.cc[index];
    const ccEmail = this.props.cc[index];

    const isBlacklisted = blacklist.indexOf(ccEmail) !== -1;

    return {
      color: (hasError && 'red') || (isBlacklisted && 'orange') || 'blue',
      content: `${label.text}`,
      icon: 'mail',
      basic: true,
    };
  };

  render() {
    const { errors, email, cc } = this.props;
    const { isCarbonCopyEnabled, blacklist } = this.state;

    const carbonCopyOptions =
      cc && cc.length
        ? cc.map(value => ({
            text: value,
            value,
          }))
        : [];

    return (
      <div>
        <Form.Field
          control={EmailContentInput}
          label="Email"
          isCarbonCopyEnabled={isCarbonCopyEnabled}
          onCarbonCopyClick={this.enableCarbonCopyInput}
          required
          id="email"
          hasError={errors.email || this.isEmailBlacklisted(blacklist, email)}
          errorMessage={
            (errors.email && 'Please enter a valid email') ||
            (this.isEmailBlacklisted(blacklist, email) && 'This email is blacklisted')
          }
          type="email"
          name="email"
          value={email}
          onBlur={this.handleBlur}
          fluid
          icon
          iconPosition="left"
          onChange={this.onFieldChange}
        />
        {this.state.isCarbonCopyEnabled && (
          <Form.Field
            control={DropdownWithErrors}
            id="email-cc"
            allowAdditions
            multiple
            selection
            search
            on
            noResultsMessage={null}
            minCharacters={0}
            label="Cc"
            hasError={some(errors.cc, error => error) || this.isAnyCcBlacklisted(cc, blacklist)}
            onAddItem={this.handleAdd}
            errorMessage={
              (errors.cc && errors.cc.length && 'Please enter a valid email') ||
              (this.isAnyCcBlacklisted(cc, blacklist) && 'You have added a blacklisted email')
            }
            icon={null}
            name="cc"
            value={cc}
            selectOnBlur={false}
            options={carbonCopyOptions}
            renderLabel={this.renderEmailLabel}
            onChange={this.onFieldChange}
          />
        )}
      </div>
    );
  }
}

export default withApollo(EmailInput);
