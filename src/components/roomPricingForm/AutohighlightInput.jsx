import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { Input } from 'semantic-ui-react';

class AutohighlightInput extends Component {
  static propTypes = {};

  handleFocus = (e, ...args) => {
    e.target.select();

    if (this.props.onFocus) {
      this.props.onFocus(e, ...args);
    }
  };

  render() {
    return <Input {...this.props} onFocus={this.handleFocus} />;
  }
}

export default AutohighlightInput;
