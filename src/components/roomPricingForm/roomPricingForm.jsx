import React, { Component } from 'react';
import PropTypes from 'prop-types';

import { Form, Divider } from 'semantic-ui-react';
import { calculatePercentageFromValues, calculatePercentageValue } from 'utils/calculationUtils';
import CurrencyIcon from '../currencyIcon';
import AutohighlightInput from './AutohighlightInput';
import './roomPricingForm.scss';

function getTotal(officialPrice = 0, discountPrice = 0, nights = 1, extrasPrice = 0) {
  return (officialPrice - discountPrice) * nights + extrasPrice;
}

class RoomPricingForm extends Component {
  static propTypes = {
    currency: PropTypes.string,
    idPrefix: PropTypes.string,
    officialPrice: PropTypes.any,
    discountPercentage: PropTypes.any,
    discountPrice: PropTypes.any,
    finalPrice: PropTypes.any,
    nights: PropTypes.number,
    withExtras: PropTypes.bool,
    inline: PropTypes.bool,
  };

  static defaultProps = {
    currency: 'EUR',
    idPrefix: 'id',
    withExtras: false,
    inline: true,
  };

  handleChange = (e, { name, value }) => {
    let priceValue = value;
    if (name === 'roomRate') {
      priceValue = this.getIntValue(value);
    } else if (name === 'totalRoomRate') {
      priceValue = this.getIntValue(value) / this.props.nights;
      name = 'roomRate';
    } else if (name === 'officialRate') {
      priceValue = this.getIntValue(value);
    } else if (name === 'discountRate') {
      priceValue = parseInt(value, 10) === 0 ? null : parseInt(value, 10);
    } else if (name === 'serviceTotalRate') {
      priceValue = this.getIntValue(value);
    }

    this.props.onChange(name, priceValue);
  };

  getFloatValue = (value = 0) => (value / 100).toFixed(2);
  getIntValue = (value = '') => parseInt(parseFloat(value) * 100, 10);

  render() {
    const {
      currency,
      idPrefix,
      officialPrice,
      roomPrice,
      nights,
      discountPrice,
      extrasPrice,
      withExtras,
      inline,
    } = this.props;

    const officialInputId = `${idPrefix}-official`;
    const priceInputId = `${idPrefix}-price`;
    const discountInputId = `${idPrefix}-discount`;
    const finalPriceInputId = `${idPrefix}-final`;
    const extrasPriceInputId = `${idPrefix}-extras`;

    const currencyIcon = <CurrencyIcon currency={currency} />;

    const officialRate = parseFloat(this.getFloatValue(officialPrice));
    const averageRate = parseFloat(this.getFloatValue(officialPrice - discountPrice));
    const discountRate = parseFloat(calculatePercentageFromValues(discountPrice, officialPrice));
    const totalRoomRate = parseFloat(this.getFloatValue((officialPrice - discountPrice) * nights));
    const serviceTotalRate = parseFloat(this.getFloatValue(extrasPrice));
    const finalRate = parseFloat(
      this.getFloatValue(getTotal(officialPrice, discountPrice, nights, extrasPrice))
    );

    const formStyle = inline
      ? {
          flexDirection: 'column',
        }
      : {};

    const fieldStyle = inline
      ? {
          margin: '3px 0',
        }
      : {};

    return (
      <Form widths="equal" style={{ textAlign: 'right' }}>
        <Form.Group inline={!inline} style={formStyle}>
          <Form.Input
            id={officialInputId}
            inline={inline}
            style={fieldStyle}
            type="number"
            name="officialRate"
            onChange={this.handleChange}
            control={AutohighlightInput}
            label="Official Average Price"
            className={`qb-input qb-input-colored qb-input-colored-disabled ${
              roomPrice < officialPrice ? 'is-disabled' : ''
            }`}
            icon={currencyIcon}
            iconPosition="left"
            value={officialRate}
          />
          <Form.Input
            id={priceInputId}
            inline={inline}
            style={fieldStyle}
            control={AutohighlightInput}
            step="10"
            type="number"
            className="qb-input qb-input-colored qb-input-colored-orange"
            icon={currencyIcon}
            iconPosition="left"
            name="roomRate"
            label="Your Average Price"
            value={averageRate}
            onChange={this.handleChange}
          />
          <Form.Input
            inline={inline}
            style={fieldStyle}
            control={AutohighlightInput}
            type="number"
            step="1"
            className="qb-input qb-input-colored qb-input-colored-red"
            id={discountInputId}
            label="Discount Percentage"
            icon="percent"
            name="discountRate"
            iconPosition="left"
            value={discountRate}
            onChange={(e, { value, name }) =>
              this.handleChange(e, { value: calculatePercentageValue(value, officialPrice), name })
            }
          />
          <Divider />
          <Form.Input
            inline={inline}
            style={fieldStyle}
            control={AutohighlightInput}
            type="number"
            step="10"
            className="qb-input qb-input-colored qb-input-colored-blue"
            id={finalPriceInputId}
            icon={currencyIcon}
            iconPosition="left"
            label="Accommodation"
            name="totalRoomRate"
            value={totalRoomRate}
            onChange={this.handleChange}
          />
          {withExtras && (
            <Form.Input
              inline={inline}
              style={fieldStyle}
              control={AutohighlightInput}
              type="number"
              step="10"
              className="qb-input qb-input-colored qb-input-colored-blue"
              id={extrasPriceInputId}
              icon={currencyIcon}
              iconPosition="left"
              label="Extras"
              name="serviceTotalRate"
              value={serviceTotalRate}
              onChange={this.handleChange}
            />
          )}
          {withExtras && (
            <Form.Input
              inline={inline}
              style={fieldStyle}
              control={AutohighlightInput}
              type="number"
              step="10"
              className="qb-input qb-input-colored qb-input-colored-text"
              id={finalPriceInputId}
              icon={currencyIcon}
              iconPosition="left"
              label="Total"
              name="totalRoomRate"
              value={finalRate}
              disabled
            />
          )}
        </Form.Group>
      </Form>
    );
  }
}

RoomPricingForm.propTypes = {};

export default RoomPricingForm;
