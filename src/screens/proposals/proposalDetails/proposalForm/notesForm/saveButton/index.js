import { graphql, compose } from 'react-apollo';
import { connect } from 'react-redux';

import { some, get } from 'lodash';

import updateRequestNotes from 'mutations/updateRequestNotes.gql';
import getRequest from 'queries/getRequest.gql';
import getAttributeSchema from 'queries/getAttributeSchema.gql';
import closeProposalNotesForm from 'state/actions/proposals/closeProposalNotesForm';
import { getAttributesErrors } from 'state/selectors/proposalValidation';
import doProposalValidation from 'state/actions/proposals/doProposalValidation';

import SaveButton from './saveButton';

const withAttributeSchemaStateVariables = connect(state => ({
  propertyCode: get(state, 'proposals.proposal.request.propertyCode'),
  language: get(state, 'proposals.proposal.theme.language')
}));

const withAttributeSchema = graphql(getAttributeSchema, {
  props: ({ data }) => ({
    isSchemaLoading: data.loading,
    schema: data.attributeSchema
  }),
  skip: ownProps => !ownProps.propertyCode || !ownProps.language
});

const SaveButtonMutationContainer = graphql(updateRequestNotes, {
  props: ({ mutate, ownProps }) => ({
    onSave: () => {
      const payload = {
        notes: ownProps.proposal.request.notes || '',
        tags: ownProps.proposal.request.tags || [],
        channel: ownProps.proposal.request.channel,
        country: ownProps.proposal.contact.country,
        attributes: ownProps.proposal.attributes || {}
      };

      if (ownProps.areNotesValid) {
        return mutate({
          variables: {
            notes: Object.assign({}, payload, {
              id: ownProps.proposal.id
            })
          },
          update: store => {
            const proposalData = store.readQuery({
              query: getRequest,
              variables: { id: ownProps.proposal.id }
            });

            const proposal = {
              ...proposalData.proposal,
              request: {
                ...proposalData.proposal.request,
                notes: payload.notes,
                tags: payload.tags,
                channel: payload.channel
              },
              contact: {
                ...proposalData.proposal.contact,
                country: payload.country
              },
              attributes: payload.attributes
            };

            store.writeQuery({ query: getRequest, data: { proposal } });
          }
        }).then(() => ownProps.onSave());
      }

      ownProps.validate();
      return new Promise(resolve => resolve());
    }
  })
});

const SaveButtonStateContainer = connect(
  (state, props) => ({
    proposal: state.proposals.proposal,
    areNotesValid: !some(getAttributesErrors(state, props), error => error)
  }),
  dispatch => ({
    onSave: () => dispatch(closeProposalNotesForm()),
    validate: () => dispatch(doProposalValidation())
  })
);

export default compose(
  withAttributeSchemaStateVariables,
  withAttributeSchema,
  SaveButtonStateContainer,
  SaveButtonMutationContainer
)(SaveButton);
