/**
 * Application Configuration
 */
const { SENTRY_DNS } = process.env;
const env = process.env.NODE_ENV || 'local';
const pkg = require('./../package.json');

const config = {
  env,
  PRODUCTION: env === 'production',
  STAGING: env === 'staging',
  DEVELOPMENT: env === 'development',
  CI: env === 'ci',
  /**
   * local is the developer's local environment.
   * We want webpack to add development assets
   * only in the local environment.
   */
  LOCAL: env === 'local',
  version: pkg.version,
};

const environments = {
  development: {
    sentry: {
      dsn: SENTRY_DNS,
      enabled: false,
    },
    intercom: {
      appId: 'oesqe2lx',
    },
    graphQL: {
      endpoint: 'https://api.quotelier.net/staging/graphql',
      token:
        'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.k4mbu1RzCfIZvG7ncz0pWLMRewge186ulWLbICTPvYA',
    },
    host: 'https://dev-assets.quotelier.net',
  },
  staging: {
    sentry: {
      dsn: SENTRY_DNS,
      enabled: true,
    },
    intercom: {
      appId: 'oesqe2lx',
    },
    graphQL: {
      endpoint: 'https://api.quotelier.net/staging/graphql',
      token:
        'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.k4mbu1RzCfIZvG7ncz0pWLMRewge186ulWLbICTPvYA',
    },
    host: 'https://sta-assets.quotelier.net',
  },
  ci: {
    sentry: {
      dsn: SENTRY_DNS,
      enabled: false,
    },
    intercom: {
      appId: 'oesqe2lx',
    },
    graphQL: {
      endpoint: 'https://api.quotelier.net/devel/graphql',
      token:
        'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.JE4jeNyXpbimoAmuqTixnHnMO6yK4DaYJ89slMSjJB0',
    },
    host: `https://quotelier.gitlab.io/-/reactapp/-/jobs/${process.env.CI_JOB_ID}/artifacts/build`
  },
  production: {
    sentry: {
      dsn: SENTRY_DNS,
      enabled: true,
    },
    intercom: {
      appId: 'leuv40kv',
    },
    graphQL: {
      endpoint: 'https://api.quotelier.net/production/graphql',
      token:
        'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.9IGvLYMynCTONVunYw1UoF7oWAiDFA8bqtjhQjIMGs4',
    },
    host: 'https://assets.quotelier.net',
  },
};

/**
 * @TODO: Check for a local configuration and parse it if exists
 * the local configuration should not be git tracked
 */
module.exports = Object.assign({}, config, environments[config.env] || environments.development);
