const precss = require('precss');
const autoprefixer = require('autoprefixer');
const browsersList = require('./browsersList');

/**
 * PostCSS Configuration Function
 * that runs on webpack compile at runtime.
 */
function postCssConfig(webpack) {
  return [
    // We use autoprefixer to add prefixes on css properties
    // for better browser support
    autoprefixer({ browsers: browsersList }),

    // PreCSS is a postcss plugin that contains a collection
    // of usefull plugins to emulate SASS like syntax in postcss files.
    // We can pass the configuration for each plugin in its options param.
    precss({
      // Use the import plugin on scss files and add a dependancy
      // to webpack to recompile when those files change
      import: {
        prefix: '',
        extension: 'scss',
        addDependancyTo: webpack
      }
    })
  ];
}

module.exports = postCssConfig;
