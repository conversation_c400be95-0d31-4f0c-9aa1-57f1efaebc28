const webpack = require('webpack');
const path = require('path');
const url = require('url');
const postCssConfig = require('./postcss.config');
const config = require('./');
const ExtractTextPlugin = require('extract-text-webpack-plugin');
const SentryPlugin = require('webpack-sentry-plugin');

/**
 * Stringify values to pass to DefinePlugin
 */
const packinize = (obj) => {
  const ret = Object.assign({}, obj);

  (function pack(o) {
    Object.keys(o).forEach((key) => {
      const v = o[key];
      if (typeof v === 'string' || typeof v === 'boolean') {
        o[key] = JSON.stringify(v);
      } else if (typeof v === 'object' && typeof v !== 'function') {
        pack(v);
      }
    });
  }(ret));

  return ret;
};

/**
 * Checks if the given resourse is in the node_modules dir
 */
const isVendor = ({ resource }) =>
  resource && resource.indexOf('node_modules') >= 0 && resource.match(/\.js$/);

const babelPresets = [['es2015', { modules: false }], 'react', 'stage-1'];

if (config.LOCAL) {
  babelPresets.push('react-hmre');
}

/**
 * Rules for loading each file
 */

// JS / JSX Rules
const jsRules = {
  test: /\.jsx?$/,
  exclude: /node_modules/,
  loader: 'babel-loader',
  options: {
    presets: babelPresets,
    // Don't read from .babelrc
    babelrc: false
  }
};

// GraphQL / GQL Rules
const gqlRules = {
  test: /\.(graphql|gql)$/,
  exclude: /node_modules/,
  use: 'graphql-tag/loader'
};

// CSS / PostCSS Rules
const pcssRules = {
  test: /\.(css|pcss)$/,
  exclude: /node_modules/,
  use: ExtractTextPlugin.extract({
    fallback: 'style-loader',
    use: [
      {
        loader: 'css-loader',
        options: {
          minimize: !config.LOCAL
        }
      },
      {
        loader: 'postcss-loader',
        options: {
          plugins: postCssConfig
        }
      }
    ]
  })
};

// SCSS Rules
const scssRules = {
  test: /\.scss/,
  use: ExtractTextPlugin.extract({
    fallback: 'style-loader',
    use: [
      {
        loader: 'css-loader',
        options: {
          minimize: !config.LOCAL
        }
      },
      'sass-loader'
    ]
  })
};

const hotMiddleWare = ['webpack/hot/dev-server', 'webpack-hot-middleware/client'];

const commonPlugins = [
  new webpack.DefinePlugin({
    __CONFIG__: packinize(config)
  })
];

const devPlugins = [
  new webpack.HotModuleReplacementPlugin(),
  new webpack.NoEmitOnErrorsPlugin(),
  new ExtractTextPlugin({
    filename: '[name].css',
    allChunks: true,
    disable: config.LOCAL
  })
];

const prodPlugins = [
  new ExtractTextPlugin({
    filename: '[name].css',
    allChunks: true,
    disable: config.LOCAL
  }),
  new webpack.optimize.UglifyJsPlugin({
    compress: {
      unused: true,
      dead_code: true,
      warnings: false
    },
    sourceMap: true
  }),
  new webpack.optimize.CommonsChunkPlugin({
    async: 'vendor',
    minChunks: isVendor
  }),
  // Adds the __webpack_hash__ global variable to the app bundle
  new webpack.ExtendedAPIPlugin(),
  // Automatically updates the sourcemaps of the bundle to sentry
  config.sentry.enabled &&
  new SentryPlugin({
    organisation: 'headinbeds-00',
    project: 'quotelier-react',
    apiKey: process.env.SENTRY_TOKEN,
    suppressErrors: true,
    deleteAfterCompile: true,
    // Add publicPath to sourcemaps
    // The ~ will be used by sentry to match any host
    filenameTransform: filename => `~/js/${filename}`,
    // We use the webpackBuildHash to distinquish the version of the bundle in staging
    // and the app's npm version in the production
    release: webpackBuildHash => (config.PRODUCTION ? config.version : webpackBuildHash)
  })
];

const plugins = config.LOCAL ? commonPlugins.concat(devPlugins) : commonPlugins.concat(prodPlugins);

const devtool = config.LOCAL ? '#eval-source-map' : 'source-map';

const entry = {
  app: config.LOCAL ? hotMiddleWare.concat('./index.js') : './index.js'
};


let publicPath = url.resolve(config.host, '/js/');

if (config.CI) {
  publicPath = `${config.host}/js/`;
} else if (config.LOCAL) {
  publicPath = '/js/';
}

/**
 * Webpack Configuration
 */
const webpackConfig = {
  devtool,
  context: path.join(__dirname, '../', 'src'),

  resolve: {
    extensions: ['.js', '.jsx', '.gql', '.scss', '.pcss'],
    modules: [path.resolve(__dirname, '../', 'src'), 'node_modules'],
    alias: {
      handlebars: 'handlebars/dist/handlebars.js'
    }
  },

  entry,

  output: {
    path: path.join(__dirname, '../', 'build', 'js'),
    publicPath,
    filename: '[name].js',
    chunkFilename: '[chunkhash].js'
  },

  plugins,
  module: {
    rules: [jsRules, gqlRules, pcssRules, scssRules]
  }
};

module.exports = webpackConfig;
